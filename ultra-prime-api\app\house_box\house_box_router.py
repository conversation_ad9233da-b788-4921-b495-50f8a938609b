from typing import Dict, Any

from fastapi import APIRouter, Path, Query

from app.house_box.house_box_model import CategoryCreateRequest, CategoryUpdateRequest, SupplyCreateRequest, \
    SupplyUpdateRequest
from app.house_box.house_box_service import HouseBoxService, PrimeHouseBoxCategoryService, PrimeHouseBoxSupplyService
from app.util.base_model import ApiResponse
from config import setup_logging

router = APIRouter(prefix="/house-box", tags=["house-box路由"])
TAG = __name__
logger = setup_logging()

house_box_service = HouseBoxService()


# 数据源映射
def get_service_by_ds(ds: str):
    service_mapping = {
        "category": PrimeHouseBoxCategoryService(),
        "supply": PrimeHouseBoxSupplyService()
    }
    return service_mapping.get(ds)


def get_model_by_ds(ds: str):
    model_mapping = {
        "category": (CategoryCreateRequest, CategoryUpdateRequest),
        "supply": (SupplyCreateRequest, SupplyUpdateRequest)
    }
    return model_mapping.get(ds)


@router.post("/{ds}/create", response_model=ApiResponse)
async def create_item(
        ds: str = Path(..., description="数据源: category, supply, inventory, usage, purchase"),
        item_data: Dict[str, Any] = None
):
    try:
        service = get_service_by_ds(ds)
        if not service:
            return ApiResponse(code=400, description=f"不支持的数据源: {ds}", data=None)

        model_classes = get_model_by_ds(ds)
        if not model_classes:
            return ApiResponse(code=400, description=f"未找到数据源对应的模型: {ds}", data=None)

        create_model_class, _ = model_classes
        if item_data:
            # 验证数据
            validated_data = create_model_class(**item_data).model_dump(exclude_unset=True)
        else:
            validated_data = {}

        # 根据不同数据源调用不同的创建方法
        if ds == "category":
            result = service.create_category(validated_data)
        elif ds == "supply":
            result = service.create_supply(validated_data)
        else:
            return ApiResponse(code=400, description=f"不支持的操作: {ds}", data=None)

        return ApiResponse(data=result.model_dump())
    except Exception as e:
        return ApiResponse(code=500, description=f"创建失败: {str(e)}", data=None)


@router.get("/{ds}/item/{item_id}", response_model=ApiResponse)
async def get_item_by_id(
        ds: str = Path(..., description="数据源: category, supply, inventory, usage, purchase"),
        item_id: int = Path(..., description="项目ID")
):
    try:
        service = get_service_by_ds(ds)
        if not service:
            return ApiResponse(code=400, description=f"不支持的数据源: {ds}", data=None)

        # 根据不同数据源调用不同的查询方法
        if ds == "category":
            result = service.get_category_by_id(item_id)
        elif ds == "supply":
            result = service.get_supply_by_id(item_id)
        else:
            return ApiResponse(code=400, description=f"不支持的操作: {ds}", data=None)

        if result is None:
            return ApiResponse(code=404, description="项目未找到", data=None)

        return ApiResponse(data=result.model_dump())
    except Exception as e:
        return ApiResponse(code=500, description=f"查询失败: {str(e)}", data=None)


@router.get("/{ds}/pagination", response_model=ApiResponse)
async def get_pagination(
        ds: str = Path(..., description="数据源: category, supply, inventory, usage, purchase"),
        page: int = Query(1, ge=1, description="页码"),
        size: int = Query(20, ge=1, le=100, description="每页数量")
):
    try:
        service = get_service_by_ds(ds)
        if not service:
            return ApiResponse(code=400, description=f"不支持的数据源: {ds}", data=None)
        # 根据不同数据源调用不同的分页查询方法
        if ds == "category":
            result = service.get_categories_with_pagination(page=page, size=size)
        elif ds == "supply":
            result = service.get_supplies_with_pagination(page=page, size=size)
        else:
            return ApiResponse(code=400, description=f"不支持的操作: {ds}", data=None)

        return ApiResponse(data=result)
    except Exception as e:
        return ApiResponse(code=500, description=f"查询失败: {str(e)}", data=None)


@router.get("/{ds}/list", response_model=ApiResponse)
async def get_list(ds: str):
    try:
        service = get_service_by_ds(ds)
        if not service:
            return ApiResponse(code=400, description=f"不支持的数据源: {ds}", data=None)
        if ds == "category":
            result = service.get_all_categories()
        elif ds == "supply":
            result = service.get_all_supplies()
        else:
            return ApiResponse(code=400, description=f"不支持的操作: {ds}", data=None)
        return ApiResponse(data=result)
    except Exception as e:
        return ApiResponse(code=500, description=f"查询失败: {str(e)}", data=None)


@router.put("/{ds}/update/{item_id}", response_model=ApiResponse)
async def update_item(
        ds: str = Path(..., description="数据源: category, supply, inventory, usage, purchase"),
        item_id: int = Path(..., description="项目ID"),
        item_data: Dict[str, Any] = None
):
    try:
        service = get_service_by_ds(ds)
        if not service:
            return ApiResponse(code=400, description=f"不支持的数据源: {ds}", data=None)

        model_classes = get_model_by_ds(ds)
        if not model_classes:
            return ApiResponse(code=400, description=f"未找到数据源对应的模型: {ds}", data=None)

        _, update_model_class = model_classes
        if item_data:
            # 验证数据
            validated_data = update_model_class(**item_data).model_dump(exclude_unset=True)
        else:
            validated_data = {}

        # 根据不同数据源调用不同的更新方法
        if ds == "category":
            result = service.update_category(item_id, validated_data)
        elif ds == "supply":
            result = service.update_supply(item_id, validated_data)
        else:
            return ApiResponse(code=400, description=f"不支持的操作: {ds}", data=None)

        if result is None:
            return ApiResponse(code=404, description="项目未找到", data=None)

        return ApiResponse(data=result.model_dump())
    except Exception as e:
        return ApiResponse(code=500, description=f"更新失败: {str(e)}", data=None)


@router.delete("/{ds}/delete/{item_id}", response_model=ApiResponse)
async def delete_item(
        ds: str = Path(..., description="数据源: category, supply, inventory, usage, purchase"),
        item_id: int = Path(..., description="项目ID")
):
    try:
        service = get_service_by_ds(ds)
        if not service:
            return ApiResponse(code=400, description=f"不支持的数据源: {ds}", data=None)

        # 根据不同数据源调用不同的删除方法
        if ds == "category":
            success = service.delete_category(item_id)
        elif ds == "supply":
            success = service.delete_supply(item_id)
        else:
            return ApiResponse(code=400, description=f"不支持的操作: {ds}", data=None)

        if not success:
            return ApiResponse(code=404, description="项目未找到", data=None)

        return ApiResponse(data={"message": "删除成功"})
    except Exception as e:
        return ApiResponse(code=500, description=f"删除失败: {str(e)}", data=None)
