import subprocess

from moviepy import vfx


class VideoEffect:
    """视频转场特效类"""

    def fadein_transition(self, clip, t: float):
        """淡入特效"""
        return clip.with_effects([vfx.FadeIn(t)])

    def fadeout_transition(self, clip, t: float):
        """淡出特效"""
        return clip.with_effects([vfx.FadeOut(t)])

    def slidein_transition(self, clip, t: float, side: str):
        """滑入特效"""
        return clip.with_effects([vfx.SlideIn(t, side)])

    def slideout_transition(self, clip, t: float, side: str):
        """滑出特效"""
        return clip.with_effects([vfx.SlideOut(t, side)])

    def crossfade_transition(self, clip, t: float):
        """交叉淡化特效"""
        return clip.with_effects([vfx.CrossFadeIn(t)])


def is_nvenc_supported():
    """
    检查当前系统 ffmpeg 是否支持 h264_nvenc 编码器
    """
    try:
        result = subprocess.run(
            ['ffmpeg', '-encoders'],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            check=True
        )
        # 检查输出中是否有 h264_nvenc
        return 'h264_nvenc' in result.stdout
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False
