from app.video.video_model import VideoGenerationRequest
from app.video.video_service import DashscopeVideoService

if __name__ == '__main__':
    video_service = DashscopeVideoService()
    request = VideoGenerationRequest(
        task_id='607457840696791040',
        prompt='远景镜头静止，晨雾与火星缓缓飘动，火光微弱跳动',
        image_path='607458189880987648.png'
    )
    video_url = video_service.submit_video_task(request)
    print(video_url)
