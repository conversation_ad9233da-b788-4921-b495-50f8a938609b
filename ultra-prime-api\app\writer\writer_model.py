from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel
from sqlmodel import SQLModel, Field

from app.util.snowflake_utils import snow


class WriterRequest(BaseModel):
    agent_prompt: str
    user_prompt: str


class WriterAgentModelSchema(SQLModel, table=True):
    __tablename__ = 'aigc_writer_agent_model'

    id: int = Field(default_factory=lambda: str(snow.next_id()), primary_key=True, title="ID主键")
    classify_id: int
    agent_icon: str
    agent_name: str
    hint_content: str
    agent_prompt: str
    agent_description: str
    agent_status: str
    agent_sort: int
    remark: Optional[str] = Field(default=None, max_length=1024)
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)


class WriterAgentClassifySchema(SQLModel, table=True):
    __tablename__ = 'aigc_writer_agent_classify'

    id: int = Field(default_factory=lambda: str(snow.next_id()), primary_key=True, title="ID主键")
    classify_name: str
    remark: Optional[str] = Field(default=None, max_length=1024)
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)


####################ScriptStory####################
class ScriptStoryVideoSearchTerms(BaseModel):
    search_terms: List[str]


####################PictureStory####################

class PictureStoryScene(BaseModel):
    scene_name: str
    role_names: str
    scene_description: str
    scene_prompt: str
    scene_negative_prompt: str
    narration: str


class PictureStoryRole(BaseModel):
    role_name: str
    role_type: str
    role_description: str
    role_prompt: str
    role_negative_prompt: str


class PictureStoryShot(BaseModel):
    scenes: List[PictureStoryScene]
    roles: List[PictureStoryRole]


####################FullStory####################

class FullStoryCharacter(BaseModel):
    character_name: str
    character_description: str
    generate_character_prompt: str


class FullStoryDialogue(BaseModel):
    drama_no: int
    role: str
    line: str
    emotion: str


class FullStoryDramaScene(BaseModel):
    drama_no: int
    camera_angle: str
    shot_type: str
    camera_movement: str
    characters: List[str]
    scene_description: str
    scene_drawing_prompt: str
    video_generation_prompt: str
    sound_effect: str
    dialogues: List[str]


class FullStoryDrama(BaseModel):
    scenes: List[FullStoryDramaScene]
    characters: List[FullStoryCharacter]
    dialogues: List[FullStoryDialogue]
