import queue
import threading
from concurrent.futures import ThreadPoolExecutor


class BlockingBoundedExecutor:
    """
    线程池，最多 max_workers 个任务并发执行，
    多余任务进入队列等待，队列满时 submit() 会阻塞，直到有空位。
    """

    def __init__(self, max_workers, max_queue):
        self.max_workers = max_workers
        self.work_queue = queue.Queue(maxsize=max_queue)  # 控制排队数量
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self._shutdown = False
        self._worker_thread = threading.Thread(target=self._worker, daemon=True)
        self._worker_thread.start()

    def submit(self, fn, *args, **kwargs):
        if self._shutdown:
            raise RuntimeError("Executor is shutdown")
        # block=True: 队列满时会阻塞，直到有空位
        self.work_queue.put((fn, args, kwargs))

    def _worker(self):
        while not self._shutdown:
            try:
                fn, args, kwargs = self.work_queue.get(timeout=1)
                future = self.executor.submit(fn, *args, **kwargs)
                self.work_queue.task_done()
            except queue.Empty:
                continue

    def shutdown(self, wait=True):
        self._shutdown = True
        if wait:
            # 等待所有排队任务完成
            self.work_queue.join()
        self.executor.shutdown(wait=wait)
