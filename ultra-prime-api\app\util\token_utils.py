import jwt

from config import Config, setup_logging

TAG = __name__
logger = setup_logging()


def decode_jwt(token):
    # 去除可能存在的Bearer前缀
    if token.startswith('Bearer '):
        token = token.replace('Bearer ', '')
    payload = jwt.decode(token, Config.JWT_SECRET, algorithms=["HS256"])
    openid = payload.get("openid")
    user_id = payload.get("user_id")
    return openid, user_id


def create_jwt(openid, user_id):
    return jwt.encode(
        {"openid": openid, "user_id": user_id},
        Config.JWT_SECRET,
        algorithm="HS256"
    )
