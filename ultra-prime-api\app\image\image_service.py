from abc import ABC, abstractmethod
from concurrent.futures import ThreadPoolExecutor

import requests
from sqlalchemy import func
from sqlmodel import Session, select

from app.image.image_model import ImageUserWorkSchema, ImageStyleSchema, ImageGenerationRequest
from app.util import const
from app.util.base_model import PaginationModel
from app.util.bucket_utils import MinioBucketUtils
from app.util.snowflake_utils import snow
from app.util.state_manager import RedisStateManager
from config import Config, setup_logging
from dependencies import get_session

TAG = __name__
logger = setup_logging()

# 创建线程池用于异步任务
executor = ThreadPoolExecutor(max_workers=4)


class ImageBaseService(ABC):

    def __init__(self):
        pass

    def _get_session(self) -> Session:
        # 每次操作都获取新的session
        return next(get_session())

    @abstractmethod
    def generate_image_task(self, request: ImageGenerationRequest, user_id: str) -> str:
        """提交图片生成任务，返回任务ID或图片ID"""
        pass

    def query_image_work(self, page: int, size: int, user_id: str):
        session = self._get_session()
        try:
            offset = (page - 1) * size
            total_statement = select(func.count(ImageUserWorkSchema.id))
            total = session.exec(total_statement).one()

            statement = select(ImageUserWorkSchema).where(
                ImageUserWorkSchema.user_id == user_id
            ).order_by(ImageUserWorkSchema.create_time.desc()).offset(offset).limit(size)
            records = session.exec(statement).all()

            return PaginationModel(
                total=total,
                page=page,
                size=size,
                records=records
            )
        finally:
            session.close()

    def query_image_task(self, task_id: str):
        state_manager = RedisStateManager(task_id)
        task = state_manager.get_task()
        if task:
            return {
                'state': str(task['state']),
                'progress': int(task['progress'])
            }
        else:
            return {
                'state': '1',
                'progress': 0
            }

    def get_image_style(self):
        session = self._get_session()
        try:
            statement = select(ImageStyleSchema).where(
                ImageStyleSchema.style_status == '1'
            ).order_by(ImageStyleSchema.style_sort)
            res = session.exec(statement).all()
            return [
                {
                    "style_code": r.style_code,
                    "style_name": r.style_name,
                    "preview_url": r.preview_url
                } for r in res
            ]
        finally:
            session.close()


class ImageOpenAIService(ImageBaseService):

    def __init__(self):
        super().__init__()

    def generate_image_async(self, request: ImageGenerationRequest):
        payload, headers = self._setting_task_params(request)
        return self._generate_image_sync(payload, headers, None, None)

    def _generate_image_sync(self, payload, headers, user_id: str, state_manager):
        if state_manager:
            state_manager.update_task(progress=20)
        logger.bind(tag=TAG).info("开始生成图片")
        response = requests.request("POST", Config.OPENAI_API_URL + "/images/generations", json=payload,
                                    headers=headers)
        logger.bind(tag=TAG).info(response.json())
        if state_manager:
            state_manager.update_task(progress=40)
        try:
            if response.status_code != 200:
                logger.bind(tag=TAG).error(f"图片生成失败: {response.json()}")
                raise Exception(f"图片生成失败")
            else:
                if response.json().get('images'):
                    response_url = response.json().get("images")[0].get("url")
                    if state_manager:
                        state_manager.update_task(progress=60)
                    minio_bucket_utils = MinioBucketUtils()
                    image_url = minio_bucket_utils.convert_to_bucket_url(response_url)
                    if state_manager:
                        state_manager.update_task(progress=80)
                    if user_id:
                        session = self._get_session()
                        try:
                            # 保存到个人作品库
                            image_work = ImageUserWorkSchema(
                                user_id=user_id,
                                image_url=image_url,
                                prompt=payload["prompt"],
                                negative_prompt=payload["negative_prompt"] if payload.get('negative_prompt',
                                                                                          '') else '',
                                image_style='common',
                                image_size='16:9' if payload["image_size"] == '1280x720' else '9:16'
                            )
                            session.add(image_work)
                            session.commit()
                            if state_manager:
                                state_manager.update_task(progress=100, state=const.TASK_STATE_COMPLETE)
                        except Exception as e:
                            if state_manager:
                                state_manager.update_task(state=const.TASK_STATE_FAILED)
                            logger.bind(tag=TAG).opt(exception=True).error(f"图片保存失败: {str(e)}")
                        finally:
                            session.close()
                    return image_url
                else:
                    raise Exception('生成图失败！')

        except Exception as e:
            if state_manager:
                state_manager.update_task(state=const.TASK_STATE_FAILED)
            logger.bind(tag=TAG).opt(exception=True).error(f"图片生成失败: {str(e)}")

    def _setting_task_params(self, request: ImageGenerationRequest):
        # 尺寸转换
        if request.image_size == '16:9':
            request.image_size = "1280x720"
        else:
            request.image_size = "720x1280"
        payload = {
            "model": Config.OPENAI_IMAGE_MODEL,
            "prompt": request.prompt,
            "image_size": request.image_size,
            "batch_size": 1,
            "num_inference_steps": 20,
            "guidance_scale": 7.5
        }
        if request.negative_prompt:
            payload["negative_prompt"] = request.negative_prompt
        if request.image:
            payload["image"] = f"data:image/png;base64,{request.image}"
        headers = {
            "Authorization": f"Bearer {Config.OPENAI_API_KEY}",
            "Content-Type": "application/json"
        }
        return payload, headers

    def generate_image_task(self, request: ImageGenerationRequest, user_id: str) -> str:
        task_id = snow.next_id()
        state_manager = RedisStateManager(task_id)
        state_manager.update_task(progress=10)
        payload, headers = self._setting_task_params(request)
        state_manager.update_task(progress=20)
        # 启动并提交异步任务
        executor.submit(self._generate_image_sync, payload, headers, user_id, state_manager)
        return task_id


class ImageDashscopeService(ImageBaseService):
    def __init__(self):
        super().__init__()

    def generate_image_task(self, request: ImageGenerationRequest, user_id: str) -> str:
        pass
