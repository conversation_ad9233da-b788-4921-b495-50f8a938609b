import asyncio

from app.voice.voice_model import SpeechSynthesisMultRole
from app.voice.voice_service import VoiceEdgeTtsService

if __name__ == '__main__':
    ssmrs = [
        SpeechSynthesisMultRole(
            role_name="旁白",
            voice="zh-CN-XiaoxiaoNeural",
            text="英国第一位下议院女议员，美国出生的南希·阿斯特与丘吉尔一向唇枪舌剑。某次吵急了，阿斯特脱口而出"
        ),
        SpeechSynthesisMultRole(
            role_name="南希·阿斯特",
            voice="zh-CN-XiaoyiNeural",
            text="温斯顿，如果我是你太太，我一定在你咖啡里下毒！"
        ),
        SpeechSynthesisMultRole(
            role_name="温斯顿·丘吉尔",
            voice="zh-CN-YunxiNeural",
            text="夫人，如果我是你丈夫，我会毫不犹豫地喝下去。"
        )
    ]

    v = VoiceEdgeTtsService()
    # 使用当前目录而不是 /tmp/
    asyncio.run(
        v.speech_synthesis_mult_role("./role_voice.mp3", ssmrs))
