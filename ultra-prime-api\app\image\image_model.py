from datetime import datetime
from typing import Optional

from pydantic import BaseModel, field_serializer
from sqlmodel import Field, SQLModel

from app.util.snowflake_utils import snow


class ImageGenerationRequest(BaseModel):
    prompt: str
    negative_prompt: Optional[str] = None
    image_size: str
    image_style: str
    image: Optional[str] = None


class ImageUserWorkSchema(SQLModel, table=True):
    __tablename__ = "aigc_image_user_work"
    id: int = Field(default_factory=lambda: str(snow.next_id()), primary_key=True, title="ID主键")
    user_id: int = Field(title="用户ID")
    image_url: str = Field(title="生成图片URL")
    prompt: str = Field(title="提示词")
    negative_prompt: Optional[str] = Field(title="反向提示词")
    image_style: str = Field(title="绘图风格")
    image_size: str = Field(title="绘图大小")
    remark: str = Field(title="备注")
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)

    @field_serializer('id')
    def serialize_id(self, v):
        return str(v)

    @field_serializer('user_id')
    def serialize_user_id(self, v):
        return str(v)


class ImageStyleSchema(SQLModel, table=True):
    __tablename__ = "aigc_image_style"
    id: int = Field(default_factory=lambda: str(snow.next_id()), primary_key=True, title="ID主键")
    style_name: str = Field(title="风格名称")
    style_code: str = Field(title="风格编码")
    preview_url: str = Field(title="风格预览图url")
    style_status: str = Field(title="风格状态")
    style_sort: int = Field(title="风格排序")
    remark: str = Field(title="备注")
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)

    @field_serializer('id')
    def serialize_id(self, v):
        return str(v)
