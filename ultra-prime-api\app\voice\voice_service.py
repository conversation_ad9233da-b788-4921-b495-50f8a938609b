import asyncio
import concurrent.futures
import os
import subprocess
import uuid
from abc import ABC, abstractmethod
from pathlib import Path
from typing import List

import edge_tts
import requests
from pydub import AudioSegment
from sqlmodel import Session, select

from app.util import common_utils
from app.util.bucket_utils import MinioBucketUtils
from app.voice.voice_model import VoiceReferenceSchema, SpeechSynthesisMultRole, VoiceDubSchema
from config import setup_logging, Config
from dependencies import get_session

TAG = __name__
logger = setup_logging()


def format_signed(val, unit):
    """
    格式化带符号的值
    :param val:
    :param unit:
    :return:
    """
    val = int(val)
    if val > 0:
        return f"+{val}{unit}"
    elif val < 0:
        return f"{val}{unit}"
    else:
        return f"+0{unit}"


class VoiceBaseService(ABC):
    def __init__(self):
        pass

    def _get_session(self) -> Session:
        # 每次操作都获取新的session
        return next(get_session())

    def seconds_to_srt_time(self, seconds: float) -> str:
        """将秒数转换为SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"

    def _merge_audio_with_ffmpeg(self, temp_files: List[str], output_file: str):
        """使用 FFmpeg 直接合并音频"""
        # 创建文件列表
        list_file = output_file.replace('.mp3', '_list.txt')
        with open(list_file, 'w') as f:
            for temp_file in temp_files:
                f.write(f"file '{temp_file}'\n")

        # 使用 FFmpeg concat
        cmd = [
            'ffmpeg', '-f', 'concat', '-safe', '0', '-y',
            '-i', list_file, '-c', 'copy', output_file
        ]
        subprocess.run(cmd, check=True)
        os.remove(list_file)

    @abstractmethod
    async def speech_synthesis_online(self, speech_text: str, voice: str, speed: int, pitch: int,
                                      volume: int) -> str:
        """
        语音合成
        :param user_id:用户id
        :param speech_text: 合成文本
        :param voice: 音色
        :param speed: 音色语速
        :param pitch: 音色语调
        :param volume: 音量增益
        :return:
        """
        pass

    @abstractmethod
    async def speech_synthesis_local(self, speech_text: str, audio_file_path: str, subtitle_file_path: str, voice: str):
        pass

    @abstractmethod
    async def model_available_spks(self, model):
        pass


class VoiceEdgeTtsService(VoiceBaseService):
    def __init__(self):
        super().__init__()

    def _generate_role_audio(self, ssmr: SpeechSynthesisMultRole, temp_file: str) -> float:
        """生成单个角色的音频文件"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        logger.bind(tag=TAG).info(f"生成角色音频: {ssmr.role_name}")
        try:
            tts = edge_tts.Communicate(
                ssmr.text.strip(),
                ssmr.voice,
                rate=format_signed(ssmr.rate, "%"),
                volume=format_signed(ssmr.volume, "%"),
                pitch=format_signed(ssmr.pitch, "Hz")
            )
            loop.run_until_complete(tts.save(temp_file))

            # 验证文件是否正确生成
            if not os.path.exists(temp_file) or os.path.getsize(temp_file) == 0:
                raise Exception(f"音频文件生成失败或为空: {temp_file}")

            # 计算音频时长，使用更安全的方式
            try:
                audio_segment = AudioSegment.from_mp3(temp_file)
                return audio_segment.duration_seconds
            except Exception as e:
                logger.bind(tag=TAG).error(f"读取音频时长失败: {temp_file}, 错误: {e}")
                # 如果无法读取，估算时长（大概每分钟150字）
                estimated_duration = len(ssmr.text) / 150 * 60
                return max(1.0, estimated_duration)
        except Exception as e:
            logger.bind(tag=TAG).error(f"生成角色音频失败: {ssmr.role_name}, 错误: {e}")
            return 1.0  # 返回默认时长
        finally:
            loop.close()

    def _split_text_by_punctuation(self, text: str) -> List[str]:
        """根据标点符号分割文本并合并标点符号到前面的句子"""
        import re

        # 使用中英文标点符号进行分割
        sentences = re.split(r'([，。！？；：、,.!?;:\s]+)', text)
        sentences = [s.strip() for s in sentences if s.strip()]

        # 合并标点符号到前面的句子
        merged_sentences = []
        i = 0
        while i < len(sentences):
            if i + 1 < len(sentences) and re.match(r'^[，。！？；：、,.!?;:\s]+$', sentences[i + 1]):
                merged_sentences.append(sentences[i] + sentences[i + 1])
                i += 2
            else:
                merged_sentences.append(sentences[i])
                i += 1

        # 去除合并后句子中的前后空格并处理文末标点
        processed_sentences = []
        for sentence in merged_sentences:
            sentence = sentence.strip()
            if sentence:
                # 只保留文末的！!?？，删除其他标点符号
                sentence = re.sub(r'[，。；：、,.;:]+$', '', sentence)  # 删除文末的其他标点
                processed_sentences.append(sentence)
        if not processed_sentences:
            processed_sentences = [text.strip()]
        return processed_sentences

    def _generate_mult_role_subtitles(self, role_durations: List[tuple], subtitle_file: str):
        """生成多角色字幕文件"""
        subtitles = []
        current_time = 0.0
        subtitle_index = 1

        for duration, text in role_durations:
            # 使用通用方法分割文本
            merged_sentences = self._split_text_by_punctuation(text)

            # 如果没有句子，使用原文本
            if not merged_sentences:
                merged_sentences = [text.strip()]

            # 计算每个句子的时长分配
            total_chars = sum(len(s) for s in merged_sentences)
            if total_chars == 0:
                continue

            sentence_start_time = current_time
            for sentence in merged_sentences:
                sentence_chars = len(sentence)
                sentence_duration = (sentence_chars / total_chars) * duration

                start_time = sentence_start_time
                end_time = sentence_start_time + sentence_duration

                start_str = self.seconds_to_srt_time(start_time)
                end_str = self.seconds_to_srt_time(end_time)

                subtitles.append(f"{subtitle_index}")
                subtitles.append(f"{start_str} --> {end_str}")
                subtitles.append(sentence)
                subtitles.append("")

                subtitle_index += 1
                sentence_start_time = end_time

            current_time += duration

        srt_content = '\n'.join(subtitles)
        if srt_content and not srt_content.endswith('\n'):
            srt_content += '\n'
        srt_content += '\n'

        with open(subtitle_file, "w", encoding="utf-8") as f:
            f.write(srt_content)

    def _generate_better_subtitles(self, text: str, word_boundaries: list) -> str:
        """基于原始文本和 word_boundaries 时间信息，结合标点切分，生成精准字幕"""
        if not word_boundaries or not text:
            return ""

        # 步骤1: 用标点切分句子（保留你的方法）
        sentences = self._split_text_by_punctuation(text)
        if not sentences:
            return ""

        # 步骤2: 将所有 word_boundary 的文本拼起来，用于对齐（处理可能的空格或分词问题）
        boundary_text = ''.join([wb['text'] for wb in word_boundaries])
        if not boundary_text:
            return ""

        # 步骤3: 构建字符级时间映射：每个字符在原文中的起始和结束时间
        char_timestamps = []  # 每个字符对应 (start_ms, end_ms)

        for wb in word_boundaries:
            start_ms = wb['offset'] / 10_000  # 转为毫秒
            end_ms = (wb['offset'] + wb['duration']) / 10_000
            for _ in wb['text']:
                char_timestamps.append((start_ms, end_ms))

        # 如果字符数不匹配，尝试修复或警告
        if len(char_timestamps) != len(boundary_text):
            # 可能因编码或空格问题，尝试截断或填充
            min_len = min(len(char_timestamps), len(boundary_text))
            char_timestamps = char_timestamps[:min_len]
            boundary_text = boundary_text[:min_len]
            if min_len == 0:
                return ""

        # 步骤4: 在 boundary_text 中查找每个 sentence 的位置
        subtitles = []
        processed_end = 0  # 已处理到 boundary_text 的哪个位置

        for idx, sentence in enumerate(sentences):
            sentence = sentence.strip()
            if not sentence:
                continue

            # 查找 sentence 在 boundary_text 中的位置
            start_pos = boundary_text.find(sentence, processed_end)
            if start_pos == -1:
                # 尝试忽略空格再找
                if sentence.replace(' ', '') in boundary_text.replace(' ', ''):
                    # 简单对齐失败，跳过或警告
                    continue
                else:
                    continue

            end_pos = start_pos + len(sentence)

            # 获取该句对应的所有字符时间戳
            sentence_timestamps = char_timestamps[start_pos:end_pos]
            if not sentence_timestamps:
                continue

            # 取第一个字符的开始时间，最后一个字符的结束时间
            start_time_ms = sentence_timestamps[0][0]  # 毫秒
            end_time_ms = sentence_timestamps[-1][1]  # 毫秒

            # 转换为秒，用于格式化
            start_seconds = start_time_ms / 1000.0
            end_seconds = end_time_ms / 1000.0

            # 格式化为 SRT 时间
            start_str = self.seconds_to_srt_time(start_seconds)
            end_str = self.seconds_to_srt_time(end_seconds)

            # 添加字幕块
            subtitles.append(f"{len(subtitles) + 1}")
            subtitles.append(f"{start_str} --> {end_str}")
            subtitles.append(sentence)
            subtitles.append("")

            # 更新已处理位置
            processed_end = end_pos

        # 生成 SRT 内容
        srt_content = '\n'.join(subtitles)
        if srt_content and not srt_content.endswith('\n'):
            srt_content += '\n'
        return srt_content + '\n'

    async def speech_synthesis_online(self, speech_text: str, voice: str, rate: int = 0, pitch: int = 0,
                                      volume: int = 0) -> str:
        speech_text = speech_text.strip()
        output_file = f"{voice}_{uuid.uuid4()}.mp3"
        tts = edge_tts.Communicate(speech_text, voice,
                                   rate=format_signed(rate, "%"),
                                   volume=format_signed(volume, "%"),
                                   pitch=format_signed(pitch, "Hz"))
        await tts.save(output_file)
        # 上传到文件服务器
        bucket_utils = MinioBucketUtils()
        file_url = bucket_utils.local_upload_file(output_file)
        logger.bind(tag=TAG).info(f"上传到文件服务器: {file_url}")
        # 删除临时文件
        os.remove(output_file)
        return file_url

    async def speech_synthesis_mult_role(self, audio_file_path: str, ssmrs: List[SpeechSynthesisMultRole]):
        """
        多角色配音，多线程生成每个角色的临时音频文件，合并后生成字幕
        :param audio_file_path: 最终音频文件路径
        :param ssmrs: 多角色语音合成配置列表
        """
        parent_dir = Path(audio_file_path).parent
        parent_dir.mkdir(parents=True, exist_ok=True)

        # 存储临时文件和时间信息
        temp_files = []
        role_durations = []

        # 多线程生成各角色音频
        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
            tasks = []
            for i, ssmr in enumerate(ssmrs):
                temp_file = parent_dir / f"temp_role_{i}_{uuid.uuid4()}.mp3"
                task = executor.submit(self._generate_role_audio, ssmr, str(temp_file))
                tasks.append((task, temp_file, ssmr))

            # 等待所有任务完成
            for task, temp_file, ssmr in tasks:
                duration = task.result()
                temp_files.append(str(temp_file))
                role_durations.append((duration, ssmr.text))

        # 使用 FFmpeg 合并音频文件
        self._merge_audio_with_ffmpeg(temp_files, audio_file_path)
        logger.bind(tag=TAG).info(f"音频文件保存完成: {audio_file_path}")

        # 生成字幕文件
        subtitle_file = audio_file_path.replace('.mp3', '.srt')
        self._generate_mult_role_subtitles(role_durations, subtitle_file)
        logger.bind(tag=TAG).info(f"字幕文件保存完成: {subtitle_file}")

        # 清理临时文件
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                os.remove(temp_file)

        # 计算总时长
        total_duration = sum(duration for duration, _ in role_durations)
        return total_duration

    async def speech_synthesis_local(self, speech_text: str, audio_file_path: str, subtitle_file_path: str = None,
                                     voice: str = 'zh-CN-XiaoxiaoNeural'):
        # 处理文本
        speech_text = speech_text.strip().replace(' ', '').replace('——', '，').replace("'", '"')
        logger.bind(tag=TAG).info(f"语音合成文本: {speech_text}")
        submaker = edge_tts.SubMaker()
        communicate = edge_tts.Communicate(speech_text, voice)

        # 收集所有时间信息
        word_boundaries = []

        # 使用流式处理同时生成音频文件和字幕数据
        with open(audio_file_path, "wb") as file:
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    file.write(chunk["data"])
                elif chunk["type"] == "WordBoundary":
                    word_boundaries.append(chunk)
                    submaker.feed(chunk)

        # 基于原始文本重新生成合理的字幕
        logger.bind(tag=TAG).info(f"生成字幕文件: {subtitle_file_path}")
        optimized_srt = self._generate_better_subtitles(speech_text, word_boundaries)
        if not subtitle_file_path:
            subtitle_file_path = audio_file_path.replace('.mp3', '.srt')
        # 生成字幕文件
        with open(subtitle_file_path, "w", encoding="utf-8") as file:
            file.write(optimized_srt)
            # 直接使用 SubMaker 生成的字幕
            # file.write(submaker.get_srt())

        # 计算音频时长
        voice_duration = submaker.cues[-1].end.total_seconds() if submaker.cues else 0.0
        logger.bind(tag=TAG).info(f"音频时长: {voice_duration}")
        return voice_duration

    async def model_available_spks(self, model):
        session = self._get_session()
        try:
            statement = select(VoiceDubSchema).where(
                VoiceDubSchema.voice_status == '1'
            )
            res = session.exec(statement).all()
            return [
                {
                    "voice_name": r.voice_name,
                    "voice_code": r.voice_code,
                    "preview_url": r.preview_url,
                    "voice_description": r.voice_description,
                    "voice_icon": r.voice_icon,
                    "voice_tag": r.voice_tag
                } for r in res
            ]
        finally:
            session.close()


class CosyVoice2Service(VoiceBaseService):
    def __init__(self):
        super().__init__()

    async def speech_synthesis_online(self, speech_text: str, voice: str, rate: int = 0, pitch: int = 0,
                                      volume: int = 0) -> str:
        pass

    async def speech_synthesis_local(self, speech_text: str, audio_file_path: str, subtitle_file_path: str, voice: str):
        pass

    async def speech_reference_audio(self, user_id: str, reference_text: str, voice_name: str, audio_file):
        # 生成唯一的文件名
        file_extension = audio_file.filename.split(".")[-1]
        file_name = uuid.uuid4()
        unique_filename = f"{file_name}.{file_extension}"
        # 保存文件到本地目录
        if not os.path.exists(Config.APP_VOICE_STORAGE):
            os.makedirs(Config.APP_VOICE_STORAGE)
        local_file_path = os.path.join(Config.APP_VOICE_STORAGE, unique_filename)
        logger.bind(tag=TAG).info(f"保存文件到本地目录: {local_file_path}")
        with open(local_file_path, "wb") as f:
            f.write(await audio_file.read())
        # 上传文件到 MinIO
        bucket_utils = MinioBucketUtils()
        file_url = bucket_utils.local_upload_file(local_file_path, unique_filename)
        logger.bind(tag=TAG).info(f"上传到文件服务器: {file_url}")
        # 保存到数据库中
        session = self._get_session()
        try:
            vrs = VoiceReferenceSchema(
                user_id=user_id,
                audio_file_url=file_url,
                voice_name=voice_name,
                audio_file_path=local_file_path,
                reference_text=reference_text
            )
            session.add(vrs)
            session.commit()
        finally:
            session.close()

    def get_speech_reference_audio(self, user_id: str):
        session = self._get_session()
        try:
            statement = select(VoiceReferenceSchema).where(
                VoiceReferenceSchema.user_id == user_id
            )
            res = session.exec(statement).all()
            return [{
                "value": str(r.id),
                "label": r.voice_name,
            } for r in res]
        finally:
            session.close()

    async def speech_synthesis_reference(self, reference_id: str, synthesis_text: str):
        session = self._get_session()
        try:
            statement = select(VoiceReferenceSchema).where(
                VoiceReferenceSchema.id == reference_id
            )
            voice_reference = session.exec(statement).one()
            if not voice_reference:
                raise Exception("未找到参考音频数据")
            else:
                audio_file_path = voice_reference.audio_file_path
                reference_text = voice_reference.reference_text
                # 调用语音克隆接口
                request_json = {
                    "tts_text": synthesis_text,
                    "prompt_text": reference_text,
                    "prompt_audio_base64": common_utils.file_to_base64(audio_file_path),
                    "stream": False,
                    "speed": 1.0,
                    "seed": 0
                }
                # 调用 CosyVoice API
                response = requests.post(Config.COSYVOICE_API_URL + "/tts/zero_shot", json=request_json)
                response.raise_for_status()
                # 读取音频数据
                wav_bytes = response.content
                # 生成文件名
                filename = f"synthesized_{uuid.uuid4()}.wav"
                file_temp_path = os.path.join(Config.APP_VOICE_STORAGE, filename)
                # 保存到本地文件
                with open(file_temp_path, "wb") as f:
                    f.write(wav_bytes)
                    logger.bind(tag=TAG).info(f"保存文件到本地目录: {file_temp_path}")
                # 上传文件到 MinIO
                bucket_utils = MinioBucketUtils()
                file_url = bucket_utils.local_upload_file(file_temp_path, filename)
                logger.bind(tag=TAG).info(f"上传到文件服务器: {file_url}")
                # 删除临时文件
                os.remove(file_temp_path)
                return file_url

        finally:
            session.close()

    async def speaker_prompt_save(self, spk_id, spk_name, prompt_text, prompt_audio):
        # 准备表单数据
        data = {
            'spk_id': spk_id,
            'spk_name': spk_name,
            'prompt_text': prompt_text
        }
        # 读取上传的文件内容
        file_content = await prompt_audio.read()

        # 准备文件数据
        files = {
            'prompt_audio': (prompt_audio.filename, file_content, prompt_audio.content_type)
        }

        response = requests.get(Config.COSYVOICE_API_URL + "/speaker_prompt/save", data=data, files=files)
        response.raise_for_status()
        return response.json()

    async def speaker_prompt_available_spks(self):
        response = requests.get(Config.COSYVOICE_API_URL + "/available_spks")
        response.raise_for_status()
        return response.json().get("speakers", [])

    async def model_available_spks(self, model):
        pass
