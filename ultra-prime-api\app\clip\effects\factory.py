from typing import Any

from app.clip.effects.crossfade import CrossFadeInEffect, CrossFadeOutEffect
from app.clip.effects.fade import FadeInEffect, FadeOutEffect
from app.clip.effects.ken_burns import KenBurnsRightEffect, KenBurnsLeftEffect, KenBurnsUpEffect, KenBurnsDownEffect
from app.clip.effects.pan import PanLeftEffect, PanRightEffect, PanUpEffect, PanDownEffect
from app.clip.effects.protocol import Effect
from app.clip.effects.zoom import ZoomInEffect, ZoomOutEffect

EFFECT_MAP: dict[str, type[Effect]] = {
    "pan_left": PanLeftEffect,
    "pan_right": PanRightEffect,
    "pan_up": PanUpEffect,
    "pan_down": PanDownEffect,
    "zoom_in": ZoomInEffect,
    "zoom_out": ZoomOutEffect,
    "fade_in": FadeInEffect,
    "fade_out": FadeOutEffect,
    "crossfade_in": CrossFadeInEffect,
    "crossfade_out": CrossFadeOutEffect,
    "ken_burns_right": KenBurnsRightEffect,
    "ken_burns_left": KenBurnsLeftEffect,
    "ken_burns_up": KenBurnsUpEffect,
    "ken_burns_down": KenBurnsDownEffect,
}


def create_effect(effect_type: str, **params: Any) -> Effect:
    """
    Create an effect.

    :param effect_type: The type of the effect.
    :param params: The effect parameters.
    :return: The effect.
    """
    effect_cls = EFFECT_MAP.get(effect_type)

    if effect_cls is None:
        raise ValueError(f"Invalid effect type: {effect_type}")

    return effect_cls(**params)
