from abc import abstractmethod
from collections.abc import Callable
from typing import Literal

from moviepy.video import fx as vfx
from moviepy.video.VideoClip import VideoClip
from pydantic import BaseModel
from pydantic.types import PositiveFloat

KenBurnsFn = Callable[[float], tuple[float, str] | tuple[str, float]]


class BaseKenBurnsEffect(BaseModel):
    """A Ken Burns effect that combines zoom and pan."""

    start_zoom: PositiveFloat = 1.0
    """Starting zoom factor."""

    end_zoom: PositiveFloat = 1.2
    """Ending zoom factor."""

    @abstractmethod
    def _pan_fn(self, clip: VideoClip) -> KenBurnsFn:
        """
        Get the pan function.

        :param clip: The clip.
        :return: The pan function.
        """
        ...

    def apply(self, clip: VideoClip) -> VideoClip:
        """
        Apply the Ken Burns effect to the clip.

        :param clip: The clip.
        :return: The clip with the effect applied.
        """

        def zoom_and_pan(t):
            """Calculate zoom factor at time t."""
            progress = t / clip.duration
            zoom = self.start_zoom + (self.end_zoom - self.start_zoom) * progress
            return zoom

        pan_fn = self._pan_fn(clip)
        return clip.with_effects([vfx.Resize(zoom_and_pan)]).with_position(pan_fn)


class KenBurnsRightEffect(BaseKenBurnsEffect):
    """A Ken Burns effect with right pan."""

    type: Literal["ken_burns_right"] = "ken_burns_right"
    """Effect type. Must be "ken_burns_right"."""

    def _pan_fn(self, clip: VideoClip) -> KenBurnsFn:
        def pan(t):
            progress = t / clip.duration if clip.duration > 0 else 0
            # 减少平移幅度，使效果更平滑
            max_pan = (clip.w * self.end_zoom - clip.w)
            x = max_pan * progress
            return -x, "center"

        return pan


class KenBurnsLeftEffect(BaseKenBurnsEffect):
    """A Ken Burns effect with left pan."""

    type: Literal["ken_burns_left"] = "ken_burns_left"
    """Effect type. Must be "ken_burns_left"."""

    def _pan_fn(self, clip: VideoClip) -> KenBurnsFn:
        def pan(t):
            progress = t / clip.duration if clip.duration > 0 else 0
            # 减少平移幅度，使效果更平滑
            max_pan = (clip.w * self.end_zoom - clip.w) * 0.3
            x = max_pan * (1 - progress)
            return -x, "center"

        return pan


class KenBurnsUpEffect(BaseKenBurnsEffect):
    """A Ken Burns effect with up pan."""

    type: Literal["ken_burns_up"] = "ken_burns_up"
    """Effect type. Must be "ken_burns_up"."""

    def _pan_fn(self, clip: VideoClip) -> KenBurnsFn:
        def pan(t):
            progress = t / clip.duration
            zoom = self.start_zoom + (self.end_zoom - self.start_zoom) * progress
            y = (clip.h * zoom - clip.h) * (1 - progress)
            return "center", -y

        return pan


class KenBurnsDownEffect(BaseKenBurnsEffect):
    """A Ken Burns effect with down pan."""

    type: Literal["ken_burns_down"] = "ken_burns_down"
    """Effect type. Must be "ken_burns_down"."""

    def _pan_fn(self, clip: VideoClip) -> KenBurnsFn:
        def pan(t):
            progress = t / clip.duration
            zoom = self.start_zoom + (self.end_zoom - self.start_zoom) * progress
            y = (clip.h * zoom - clip.h) * progress
            return "center", -y

        return pan
