import httpx
from fastapi import Depends, HTTPException, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel

from config import setup_logging, Config

security = HTTPBearer()
AUTH_CENTER_URL = "/svc01/sys/common/check-user"

TAG = __name__
logger = setup_logging()

WHITE_LIST = [
    "/speech/speaker_prompt/available_spks"
]


class TokenUser(BaseModel):
    user_id: str = '100080090001'
    has_perm: bool = True


async def get_current_user(request: Request,
                           credentials: HTTPAuthorizationCredentials = Depends(security)) -> TokenUser:
    token = credentials.credentials
    path = request.url.path  # 当前请求路径
    logger.bind(tag=TAG).info(f"当前请求路径: {path}")
    logger.bind(tag=TAG).info(f"当前请求token: {token}")
    if not Config.APP_ENABLE_AUTH:
        logger.bind(tag=TAG).info(f"权限校验未启用")
        return TokenUser()
    elif path in WHITE_LIST:
        logger.bind(tag=TAG).info(f"当前请求路径在白名单中")
        return TokenUser()
    else:
        logger.bind(tag=TAG).info(f"当前请求路径需要权限校验")
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    Config.APP_GATEWAY_URL + AUTH_CENTER_URL,
                    headers={"Authorization": f"Bearer {token}"},
                    json={"path": path, 'type': 'aigc'}
                )
                if response.status_code != 200:
                    raise HTTPException(status_code=401, detail="Invalid token or auth service error")
                data = response.json()
            except httpx.RequestError:
                raise HTTPException(status_code=503, detail="Auth service unavailable")

        user_id = data.get("userId")
        has_perm = data.get("hasPerm")

        if not user_id or not has_perm:
            raise HTTPException(status_code=403, detail="Permission denied")

        return TokenUser(
            user_id=user_id,
            has_perm=has_perm
        )
