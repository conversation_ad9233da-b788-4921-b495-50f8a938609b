import json
import time
import uuid
from typing import List

from langchain_core.messages import BaseMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate

from app.novel.novel_service import NovelService
from app.novel.novel_model import NovelChapterBlueprint
from app.novel.the_legend_novel_prompt import generate_chapter_blueprint_sys_prompt
from app.util.llm import get_llm, structured_llm
from config import Config, setup_logging

TAG = __name__
logger = setup_logging()

novel_service = NovelService()


def llm_novel_stream(messages: List[BaseMessage], novel_id: str = None, novel_param: str = None):
    chunk_id = f"chatcmpl-{uuid.uuid4().hex[:16]}"
    created = int(time.time())
    idx = 0

    # 如果有 agent_id 和 user_id，收集完整回复用于保存
    complete_response = ""

    for chunk in get_llm().stream(messages):
        if hasattr(chunk, 'content') and chunk.content:
            complete_response += chunk.content

            data = {
                "id": chunk_id,
                "object": "chat.completion.chunk",
                "created": created,
                "model": Config.OPENAI_CHAT_MODEL,
                "choices": [
                    {
                        "index": 0,
                        "delta": {
                            "role": "assistant" if idx == 0 else None,
                            "content": chunk.content
                        },
                        "finish_reason": None
                    }
                ]
            }
            idx += 1
            yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
    # 保存完整的 AI 回复到历史记录
    if complete_response:
        if 'chapter' in novel_param:
            logger.bind(tag=TAG).info(f"保存章节内容={novel_param}")
            novel_service.save_novel_chapter(novel_id, novel_param, complete_response)
        else:
            logger.bind(tag=TAG).info(f"保存参数信息={novel_param}")
            novel_service.novel_creation_params_edit(novel_id, novel_param, complete_response)

        if novel_param == 'chapter_blueprint':
            # 保存小说目录结构化数据
            llm = structured_llm(schema=NovelChapterBlueprint)
            messages = [('system', generate_chapter_blueprint_sys_prompt), ("human", "{input}")]
            prompt = ChatPromptTemplate.from_messages(messages)
            chain = prompt | llm
            response = chain.invoke({"input": complete_response})
            novel_service.save_novel_blueprint(novel_id, response)
    # 结束信号
    yield "data: [DONE]\n\n"
