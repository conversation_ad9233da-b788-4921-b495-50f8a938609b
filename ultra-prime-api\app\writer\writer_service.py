from langchain_core.messages import SystemMessage, HumanMessage
from sqlmodel import select, Session

from app.util.llm import llm_chain, structured_llm_chain, llm_stream
from app.writer.writer_model import ScriptStoryVideoSearchTerms, PictureStoryShot, WriterRequest, \
    WriterAgentModelSchema, \
    WriterAgentClassifySchema, FullStoryDrama
from app.writer.writer_prompt import script_story_user_prompt, script_story_system_prompt, \
    script_story_video_terms_system_prompt, script_story_video_terms_user_prompt, picture_story_shot_system_prompt, \
    picture_story_shot_user_prompt, full_story_drama_user_prompt, full_story_drama_system_prompt, \
    full_story_poetry_system_prompt, full_story_poetry_user_prompt, full_story_classical_chinese_user_prompt, \
    full_story_classical_chinese_system_prompt, full_story_fairy_tale_system_prompt, full_story_fairy_tale_user_prompt
from config import setup_logging
from dependencies import get_session

TAG = __name__
logger = setup_logging()


class WriterService:

    def _get_session(self) -> Session:
        # 每次操作都获取新的session
        return next(get_session())

    def get_agent(self):
        session = self._get_session()
        try:
            statement = (
                select(WriterAgentModelSchema, WriterAgentClassifySchema.classify_name)
                .join(WriterAgentClassifySchema, WriterAgentModelSchema.classify_id == WriterAgentClassifySchema.id)
            )
            results = session.exec(statement).all()
            return [
                {
                    "agent_id": str(agent.id),
                    "agent_name": agent.agent_name,
                    "classify_name": classify_name,
                    "agent_description": agent.agent_description,
                    "agent_icon": agent.agent_icon,
                    "agent_prompt": agent.agent_prompt,
                    "hint_content": agent.hint_content,
                }
                for agent, classify_name in results
            ]
        finally:
            session.close()

    def writer_completions(self, request: WriterRequest, user_id: str):
        logger.bind(tag=TAG).info(f"用户请求的系统提示词：{request.agent_prompt}")
        logger.bind(tag=TAG).info(f"用户请求的用户提示词：{request.user_prompt}")
        logger.bind(tag=TAG).info(f"用户ID：{user_id}")

        messages = [
            SystemMessage(content="你是一个乐于助人的AI助手。"),
            HumanMessage(content=request.agent_prompt.replace('[PROMPT]', request.user_prompt))
        ]
        return llm_stream(messages)

    def generate_script_story_video_script(self, story_subject: str, paragraph_number: int = 1):
        """
        生成视频脚本
        :param story_subject: 视频主题
        :param paragraph_number: 段落数量
        :return:
        """
        if not story_subject:
            raise Exception("视频主题不能为空")
        return llm_chain(
            sys_prompt=script_story_system_prompt,
            user_prompt=script_story_user_prompt.format(
                story_subject=story_subject,
                paragraph_number=paragraph_number
            )
        )

    def generate_script_story_video_terms(self, story_subject, video_script):
        if not story_subject:
            raise Exception("视频主题不能为空")
        if not video_script:
            raise Exception("视频脚本不能为空")
        return structured_llm_chain(
            sys_prompt=script_story_video_terms_system_prompt,
            user_prompt=script_story_video_terms_user_prompt.format(
                story_subject=story_subject,
                video_script=video_script
            ),
            schema=ScriptStoryVideoSearchTerms
        )

    def generate_picture_story_shot(self, existing_roles: str, story_content: str) -> PictureStoryShot:
        if not story_content:
            raise Exception("小说正文不能为空")
        return structured_llm_chain(
            sys_prompt=picture_story_shot_system_prompt,
            user_prompt=picture_story_shot_user_prompt.format(
                existing_roles=existing_roles,
                story_content=story_content
            ),
            schema=PictureStoryShot
        )

    def generate_full_story_drama(self, story_type: str, story_content: str) -> FullStoryDrama:
        if not story_content:
            raise Exception("小说正文不能为空")
        if not story_type:
            raise Exception("剧本类型不能为空")
        sys_prompt = ''
        user_prompt = ''
        if story_type == "poetry":
            sys_prompt = full_story_poetry_system_prompt
            user_prompt = full_story_poetry_user_prompt.format(
                story_content=story_content
            )

        if story_type == "drama":
            sys_prompt = full_story_drama_system_prompt
            user_prompt = full_story_drama_user_prompt.format(
                story_content=story_content
            )
        if story_type == "classical_chinese":
            sys_prompt = full_story_classical_chinese_system_prompt
            user_prompt = full_story_classical_chinese_user_prompt.format(
                story_content=story_content
            )
        if story_type == "fairy_tale":
            sys_prompt = full_story_fairy_tale_system_prompt
            user_prompt = full_story_fairy_tale_user_prompt.format(
                story_content=story_content
            )
        return structured_llm_chain(
            sys_prompt=sys_prompt,
            user_prompt=user_prompt,
            schema=FullStoryDrama
        )
