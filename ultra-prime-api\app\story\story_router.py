from typing import Optional

from fastapi import APIRouter, Depends

from app.story.full_story_service import FullStoryService
from app.story.picture_story_service import PictureStoryService
from app.story.script_story_service import ScriptStoryService
from app.story.story_model import CreateStoryDramaRequest, ScriptStoryParams, PictureStoryAnalysisRequest, \
    FullStoryCharacterRequest, FullStorySceneImageRequest, FullStorySceneVideoRequest
from app.story.story_service import StoryService
from app.util.base_model import ApiResponse
from auth import get_current_user
from config import setup_logging

router = APIRouter(prefix="/story", tags=["故事创作路由"])

TAG = __name__
logger = setup_logging()


@router.get("/user-story-work", summary="获取用户的视频作品列表")
async def get_user_story_work(user=Depends(get_current_user)):
    story_service = StoryService()
    return ApiResponse(data=story_service.get_user_story_work(user.user_id))


@router.get("/user-story-work/{mode}/{story_id}", summary="删除用户的视频作品")
async def remove_user_story_work(mode: str, story_id: str, user=Depends(get_current_user)):
    story_service = StoryService()
    return ApiResponse(data=story_service.remove_user_story_work(mode, story_id, user.user_id))


#####################full-generator#######################################

@router.get("/full-generator/detail/{story_id}", summary="获取故事详情")
async def get_story_detail(story_id: str, user=Depends(get_current_user)):
    full_story_service = FullStoryService()
    return ApiResponse(data=full_story_service.get_full_story_detail(story_id))


@router.post("/full-generator/create-drama", summary="创建故事剧本")
async def create_full_story_drama(request: CreateStoryDramaRequest, user=Depends(get_current_user)):
    full_story_service = FullStoryService()
    story_id = full_story_service.create_full_story_drama(request, user.user_id)
    return ApiResponse(data=story_id)


@router.get("/full-generator/drama/{story_id}", summary="获取故事剧本镜头")
async def get_full_story_drama(story_id: str, user=Depends(get_current_user)):
    full_story_service = FullStoryService()
    return ApiResponse(data=full_story_service.get_full_story_drama(story_id))


@router.get("/full-generator/status/{story_id}", summary="获取故事处理状态")
async def get_full_story_status(story_id: str, user=Depends(get_current_user)):
    full_story_service = FullStoryService()
    return ApiResponse(data=full_story_service.get_full_story_status(story_id))


@router.get("/full-generator/characters/{story_id}", summary="获取故事角色列表")
async def get_full_story_characters(story_id: str, user=Depends(get_current_user)):
    full_story_service = FullStoryService()
    return ApiResponse(data=full_story_service.get_full_story_characters(story_id))


@router.put("/full-generator/save-character", summary="保存角色信息")
async def save_full_story_character(request: FullStoryCharacterRequest, user=Depends(get_current_user)):
    full_story_service = FullStoryService()
    return ApiResponse(data=full_story_service.save_full_story_character(request))


@router.post("/full-generator/generate-character-image", summary="生成角色绘图")
async def generate_full_story_character_image(request: FullStoryCharacterRequest, user=Depends(get_current_user)):
    full_story_service = FullStoryService()
    full_story_service.generate_full_story_character_image(request)
    return ApiResponse()


@router.put("/full-generator/save-scene-image", summary="保存场景图片信息")
async def save_full_story_scene_image(request: FullStorySceneImageRequest, user=Depends(get_current_user)):
    full_story_service = FullStoryService()
    return ApiResponse(data=full_story_service.save_full_story_scene_image(request))


@router.post("/full-generator/generate-scene-image", summary="生成场景图片")
async def generate_full_story_scene_image(request: FullStorySceneImageRequest, user=Depends(get_current_user)):
    full_story_service = FullStoryService()
    full_story_service.generate_full_story_scene_image(request)
    return ApiResponse()


@router.put("/full-generator/save-scene-video", summary="保存场景视频信息")
async def save_full_story_scene_video(request: FullStorySceneVideoRequest, user=Depends(get_current_user)):
    full_story_service = FullStoryService()
    return ApiResponse(data=full_story_service.save_full_story_scene_video(request))


@router.post("/full-generator/generate-scene-video", summary="生成场景视频")
async def generate_full_story_scene_video(request: FullStorySceneVideoRequest, user=Depends(get_current_user)):
    full_story_service = FullStoryService()
    full_story_service.generate_full_story_scene_video(request)
    return ApiResponse()


@router.post("/full-generator/generate-voice-subtitle/{story_id}", summary="生成配音和字幕")
async def generate_full_story_voice_subtitle(story_id: str, user=Depends(get_current_user)):
    full_story_service = FullStoryService()
    full_story_service.generate_full_story_voice_subtitle(story_id)
    return ApiResponse()


@router.post("/full-generator/generate-final-video", summary="合成最终视频")
async def generate_full_story_final_video(story_id: str, user=Depends(get_current_user)):
    full_story_service = FullStoryService()
    full_story_service.generate_full_story_final_video(story_id)
    return ApiResponse()


#####################script-generator#######################################

@router.post("/script-generator/create-drama", summary='脚本故事视频生成任务')
async def script_story_generator(request: ScriptStoryParams, user=Depends(get_current_user)):
    script_story_service = ScriptStoryService()
    script_story_service.script_story_generator(request, user.user_id)
    return ApiResponse(data=request.task_id)


@router.get("/script-generator/status/{story_id}", summary='脚本故事视频生成状态')
async def script_story_status(story_id: str, user=Depends(get_current_user)):
    script_story_service = ScriptStoryService()
    return ApiResponse(data=script_story_service.script_story_status(story_id))


@router.get("/script-generator/detail/{story_id}", summary='脚本故事视频生成状态')
async def script_story_detail(story_id: str, user=Depends(get_current_user)):
    script_story_service = ScriptStoryService()
    return ApiResponse(data=script_story_service.script_story_detail(story_id))


#####################picture-generator#######################################

@router.get("/picture-generator/analysis-content/status/{story_id}", summary='绘本故事视频生成状态')
async def picture_story_analysis_content_status(story_id: str, user=Depends(get_current_user)):
    picture_story_service = PictureStoryService()
    return ApiResponse(data=picture_story_service.picture_story_analysis_content_status(story_id))


@router.post("/picture-generator/analysis-content", summary='绘本故事分析')
async def analysis_content_of_the_novel(request: PictureStoryAnalysisRequest, user=Depends(get_current_user)):
    picture_story_service = PictureStoryService()
    picture_story_service.analysis_content_of_the_novel(request.id, request.story_title, request.story_content,
                                                        request.video_aspect, request.voice_name, user.user_id)
    return ApiResponse(data=request.task_id)


@router.get("/picture-generator/detail/{story_id}", summary='获取绘本故事基本信息')
async def get_picture_story_detail(story_id: str, user=Depends(get_current_user)):
    picture_story_service = PictureStoryService()
    return ApiResponse(data=picture_story_service.get_picture_story_detail(story_id))


@router.post("/picture-generator/images/role/{story_id}", summary='绘本故事角色生图')
async def generate_images_based_on_the_role(story_id: str, role_id: Optional[str] = None,
                                            user=Depends(get_current_user)):
    picture_story_service = PictureStoryService()
    picture_story_service.generate_images_based_on_the_role(story_id, role_id)
    return ApiResponse()


@router.get("/picture-generator/images/role/{story_id}", summary='获取绘本故事角色')
async def get_picture_story_roles(story_id: str, user=Depends(get_current_user)):
    picture_story_service = PictureStoryService()
    return ApiResponse(data=picture_story_service.get_picture_story_roles(story_id))


@router.post("/picture-generator/images/scene/{story_id}", summary='绘本故事场景生图')
async def generate_images_based_on_the_scene(story_id: str, scene_id: Optional[str] = None,
                                             user=Depends(get_current_user)):
    picture_story_service = PictureStoryService()
    picture_story_service.generate_images_based_on_the_scene(story_id, scene_id)
    return ApiResponse()


@router.get("/picture-generator/images/scene/{story_id}", summary='获取绘本故事场景')
async def get_picture_story_scenes(story_id: str, user=Depends(get_current_user)):
    picture_story_service = PictureStoryService()
    return ApiResponse(data=picture_story_service.get_picture_story_scenes(story_id))


@router.get("/picture-generator/my-video-work", summary='获取用户绘本故事作品列表')
async def picture_story_my_video_work(user=Depends(get_current_user)):
    picture_story_service = PictureStoryService()
    return ApiResponse(data=picture_story_service.picture_story_my_video_work(user.user_id))


@router.post("/picture-generator/generate-audio-and-subtitle/{story_id}", summary='生成绘本故事音频和字幕')
async def generate_audio_and_subtitle(story_id: str, audio_request: dict[str, str], user=Depends(get_current_user)):
    picture_story_service = PictureStoryService()
    await picture_story_service.generate_audio_and_subtitle(story_id, audio_request['voice_name'])
    return ApiResponse()


@router.post("/picture-generator/generate-scene-video/{story_id}", summary='合成绘本故事场景视频')
async def generate_scene_video(story_id: str, user=Depends(get_current_user)):
    picture_story_service = PictureStoryService()
    await picture_story_service.generate_scene_video(story_id)
    return ApiResponse()


@router.post("/picture-generator/composite-video/{story_id}", summary='绘本故事视频合成')
async def composite_video(story_id: str, user=Depends(get_current_user)):
    picture_story_service = PictureStoryService()
    picture_story_service.composite_video(story_id)
    return ApiResponse()
