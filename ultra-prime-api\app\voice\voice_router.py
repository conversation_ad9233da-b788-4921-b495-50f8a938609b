from fastapi import APIRouter, File, UploadFile, Form, Depends

from app.util.base_model import ApiResponse
from app.voice.voice_model import SpeechSynthesisRequest, SpeechReferenceRequest
from app.voice.voice_service import VoiceEdgeTtsService, CosyVoice2Service
from auth import get_current_user
from config import setup_logging

router = APIRouter(prefix="/voice", tags=["voice路由"])
TAG = __name__
logger = setup_logging()


@router.post("/speech/synthesis/online", summary="声音合成")
async def speech_synthesis_online(request: SpeechSynthesisRequest, user=Depends(get_current_user)):
    if request.model == 'edge-tts':
        voice_service = VoiceEdgeTtsService()
    elif request.model == 'cosy-voice2':
        voice_service = CosyVoice2Service()
    else:
        raise Exception("不支持的模型")
    file_url = await voice_service.speech_synthesis_online(request.speech_text, request.voice, request.rate,
                                                           request.pitch, request.volume)
    return ApiResponse(data=file_url)


@router.post("/speech/reference/audio", summary="上传参考音频文件")
async def speech_reference_audio(reference_text: str = Form(...),
                                 voice_name: str = Form(...),
                                 file: UploadFile = File(...), user=Depends(get_current_user)):
    voice_service = CosyVoice2Service()
    await voice_service.speech_reference_audio(user.user_id, reference_text, voice_name, file)
    return ApiResponse()


@router.get("/speech/reference/audio", summary="获取参考音频列表")
async def get_speech_reference_audio(user=Depends(get_current_user)):
    voice_service = CosyVoice2Service()
    spks = voice_service.get_speech_reference_audio(user.user_id)
    return ApiResponse(data=spks)


@router.post("/speech/synthesis/reference", summary="使用参考音频进行合成")
async def speech_synthesis_reference(request: SpeechReferenceRequest, user=Depends(get_current_user)):
    voice_service = CosyVoice2Service()
    file_url = await voice_service.speech_synthesis_reference(request.reference_id, request.synthesis_text)
    return ApiResponse(data=file_url)


@router.post("/speech/speaker_prompt/save", summary="管理员新增预置音色")
async def speaker_prompt_save(spk_id: str = Form(...), spk_name=Form(...), prompt_text: str = Form(...),
                              prompt_audio: UploadFile = File(...), user=Depends(get_current_user)):
    voice_service = CosyVoice2Service()
    await voice_service.speaker_prompt_save(spk_id, spk_name, prompt_text, prompt_audio)
    return ApiResponse(data={
        "spk_id": spk_id,
        "spk_name": spk_name
    })


@router.get("/speech/speaker_prompt/available-spks", summary="管理员获取预置音色")
async def speaker_prompt_available_spks(user=Depends(get_current_user)):
    voice_service = CosyVoice2Service()
    speakers = await voice_service.speaker_prompt_available_spks()
    return ApiResponse(data=speakers)


@router.get("/speech/tts/{model}/available-spks", summary="获取音色列表")
async def model_available_spks(model: str, user=Depends(get_current_user)):
    if model == 'edge-tts':
        voice_service = VoiceEdgeTtsService()
    elif model == 'cosy-voice2':
        voice_service = CosyVoice2Service()
    else:
        raise Exception("不支持的模型")
    speakers = await voice_service.model_available_spks(model)
    return ApiResponse(data=speakers)
