import json
import time
import uuid
from typing import List

from langchain_core.messages import BaseMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from pydantic import SecretStr

from config import Config, setup_logging

TAG = __name__
logger = setup_logging()


def get_llm(temperature: float = 0.95, max_tokens: int = 4096, streaming: bool = True) -> ChatOpenAI:
    return ChatOpenAI(
        base_url=Config.DASHSCOPE_API_URL,
        api_key=SecretStr(Config.DASHSCOPE_API_KEY),
        model=Config.DASHSCOPE_CHAT_MODEL,
        frequency_penalty=0,
        temperature=temperature,
        max_tokens=max_tokens,
        top_p=0.7,
        streaming=streaming
    )


def llm_chain(sys_prompt: str, user_prompt: str):
    """
    简易的AI文本生成器
    :param sys_prompt: 系统提示词
    :param user_prompt: 用户提示词
    :return:
    """
    messages = []
    if sys_prompt:
        messages.append(('system', sys_prompt))
    messages.append(("human", "{input}"))
    prompt = ChatPromptTemplate.from_messages(messages)
    chain = prompt | get_llm()
    response = chain.invoke({"input": user_prompt})
    return response.content


def structured_llm_chain(sys_prompt: str, user_prompt: str, schema):
    """
    简易的AI文本生成器
    :param schema: 返回值结构
    :param sys_prompt: 系统提示词
    :param user_prompt: 用户提示词
    :return:
    """
    messages = [('system', sys_prompt), ("human", "{input}")]
    prompt = ChatPromptTemplate.from_messages(messages)
    chain = prompt | structured_llm(schema)
    return chain.invoke({"input": user_prompt})


def structured_llm(schema=None):
    if schema:
        logger.bind(tag=TAG).info("使用结构化生成")
        return get_llm(streaming=False).with_structured_output(schema=schema, method='json_mode')
    else:
        raise Exception('schema is required')


def llm_stream(messages: List[BaseMessage]):
    chunk_id = f"chatcmpl-{uuid.uuid4().hex[:16]}"
    created = int(time.time())
    idx = 0
    for chunk in get_llm().stream(messages):
        if hasattr(chunk, 'content') and chunk.content:
            data = {
                "id": chunk_id,
                "object": "chat.completion.chunk",
                "created": created,
                "model": Config.OPENAI_CHAT_MODEL,
                "choices": [
                    {
                        "index": 0,
                        "delta": {
                            "role": "assistant" if idx == 0 else None,
                            "content": chunk.content
                        },
                        "finish_reason": None
                    }
                ]
            }
            idx += 1
            yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"

    # 结束信号
    yield "data: [DONE]\n\n"
