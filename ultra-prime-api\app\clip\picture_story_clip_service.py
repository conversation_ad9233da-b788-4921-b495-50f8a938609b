import gc
import glob
import os
import random
import uuid
from typing import List, Callable, Optional

from PIL import ImageFont
from moviepy import ImageClip, AudioFileClip, TextClip, afx, CompositeVideoClip, VideoFileClip, \
    concatenate_videoclips, CompositeAudioClip
from moviepy.video.tools import subtitles
from moviepy.video.tools.subtitles import SubtitlesClip

from app.clip import is_nvenc_supported, VideoEffect
from app.clip.effects.factory import create_effect
from app.util import common_utils
from app.util.bucket_utils import MinioBucketUtils
from app.video.video_model import VideoAspect
from config import setup_logging

TAG = __name__
logger = setup_logging()


class PictureStoryClipService:
    def __init__(self):
        self.fps = 30
        self.video_codec = 'libx264'  # 使用 NVIDIA GPU 编码 h264_nvenc
        self.audio_codec = 'aac'
        self.video_effects = VideoEffect()  # 视频特效实例
        if is_nvenc_supported():
            self.video_codec = 'h264_nvenc'
            self.ffmpeg_params = [
                '-y',
                '-preset', 'p7',  # 最快速度
                '-cq', '23'  # 控制质量（类似CRF），值越小质量越高
            ]
        else:
            self.video_codec = 'libx264'
            self.ffmpeg_params = [
                '-y',
                '-preset', 'veryfast',
                # '-crf', '23'
            ]

    def wrap_text(self, text, max_width, font="Arial", fontsize=60):
        # Create ImageFont
        font = ImageFont.truetype(font, fontsize)

        def get_text_size(inner_text):
            inner_text = inner_text.strip()
            left, top, right, bottom = font.getbbox(inner_text)
            return right - left, bottom - top

        width, height = get_text_size(text)
        if width <= max_width:
            return text, height

        processed = True

        _wrapped_lines_ = []
        words = text.split(" ")
        _txt_ = ""
        for word in words:
            _before = _txt_
            _txt_ += f"{word} "
            _width, _height = get_text_size(_txt_)
            if _width <= max_width:
                continue
            else:
                if _txt_.strip() == word.strip():
                    processed = False
                    break
                _wrapped_lines_.append(_before)
                _txt_ = f"{word} "
        _wrapped_lines_.append(_txt_)
        if processed:
            _wrapped_lines_ = [line.strip() for line in _wrapped_lines_]
            result = "\n".join(_wrapped_lines_).strip()
            height = len(_wrapped_lines_) * height
            return result, height

        _wrapped_lines_ = []
        chars = list(text)
        _txt_ = ""
        for word in chars:
            _txt_ += word
            _width, _height = get_text_size(_txt_)
            if _width <= max_width:
                continue
            else:
                _wrapped_lines_.append(_txt_)
                _txt_ = ""
        _wrapped_lines_.append(_txt_)
        result = "\n".join(_wrapped_lines_).strip()
        height = len(_wrapped_lines_) * height
        return result, height

    def delete_files(self, files):
        """删除文件或文件列表"""
        if isinstance(files, str):
            files = [files]

        for file_path in files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.bind(tag=TAG).info(f"删除文件: {file_path}")
            except Exception as e:
                logger.bind(tag=TAG).warning(f"删除文件失败 {file_path}: {str(e)}")

    def composite_video(self, image_file_path: str, voice_file_path: str, subtitle_file_path: str,
                        video_file_path: str,
                        video_aspect: VideoAspect = VideoAspect.portrait,
                        callback: Optional[Callable[[str], None]] = None):
        """将图片转为视频，添加音频和字幕"""
        global video_clip, audio_clip
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(video_file_path)
            os.makedirs(output_dir, exist_ok=True)
            logger.bind(tag=TAG).info(f"输出目录: {output_dir}")

            # 检查输入文件是否存在
            if not os.path.exists(image_file_path):
                raise Exception(f"图片文件不存在: {image_file_path}")
            if not os.path.exists(voice_file_path):
                raise Exception(f"音频文件不存在: {voice_file_path}")
            if not os.path.exists(subtitle_file_path):
                raise Exception(f"字幕文件不存在: {subtitle_file_path}")

            # 已经生成的跳过
            if os.path.exists(video_file_path):
                logger.bind(tag=TAG).info(f"视频文件已存在，跳过合成: {video_file_path}")
                return

            logger.bind(tag=TAG).info(f"开始处理视频合成...")

            # 获取视频尺寸
            aspect = VideoAspect(video_aspect)
            video_width, video_height = aspect.to_resolution()
            logger.bind(tag=TAG).info(f"视频尺寸: {video_width}x{video_height}")

            # 加载音频获取时长
            audio_clip = AudioFileClip(voice_file_path).with_effects(
                [afx.MultiplyVolume(1.0)]
            )
            subs = subtitles.file_to_subtitles(subtitle_file_path, encoding="utf-8")
            logger.bind(tag=TAG).info(f"成功解析 {len(subs)} 行字幕")
            duration = max([tb for ((ta, tb), txt) in subs])
            logger.bind(tag=TAG).info(f"图片时长: {duration}")

            # 创建图片剪辑并调整尺寸
            image_clip = ImageClip(image_file_path, duration=duration)
            logger.bind(tag=TAG).info(f"图片剪辑创建完成")

            # 先调整图片尺寸以适应视频尺寸，保持宽高比
            image_clip = image_clip.resized(height=video_height).resized(width=video_width)
            logger.bind(tag=TAG).info(f"图片尺寸调整完成: {video_width}x{video_height}")

            # 添加轻微的缩放效果，避免抖动
            effect_list = ['zoom_in', 'ken_burns_left', 'ken_burns_up', 'ken_burns_down']
            ken_burns_effect = create_effect(random.choice(effect_list), start_zoom=1.0, end_zoom=1.2)
            image_clip = ken_burns_effect.apply(image_clip)
            logger.bind(tag=TAG).info(f"图片剪辑添加zoom_in效果完成")

            # 添加字幕
            font_path = os.path.join(common_utils.font_dir(), "STHeitiMedium.ttc")
            logger.bind(tag=TAG).info(f"字体路径: {font_path}")

            def create_text_clip(subtitle_item):
                phrase = subtitle_item[1]
                max_width = video_width * 0.9
                wrapped_txt, txt_height = self.wrap_text(
                    phrase, max_width=max_width, font=font_path, fontsize=60
                )
                interline = int(60 * 0.25)
                size = (int(max_width),
                        int(txt_height + 60 * 0.25 + (interline * (wrapped_txt.count("\n") + 1))))

                _clip = TextClip(
                    text=wrapped_txt,
                    font=font_path,
                    font_size=60,
                    color="#FFFFFF",
                    bg_color=True,
                    stroke_color="#000000",
                    stroke_width=1.5,
                    interline=interline,
                    size=size,
                )
                duration = subtitle_item[0][1] - subtitle_item[0][0]
                _clip = _clip.with_start(subtitle_item[0][0])
                _clip = _clip.with_end(subtitle_item[0][1])
                _clip = _clip.with_duration(duration)
                # 确保字幕位置固定在视频底部，不受图片特效影响
                subtitle_y = video_height - _clip.h - 80  # 距离底部80像素
                _clip = _clip.with_position(("center", subtitle_y))
                return _clip

            def make_textclip(text):
                return TextClip(
                    text=text,
                    font=font_path,
                    font_size=60,
                )

            sub = SubtitlesClip(
                subtitles=subtitle_file_path, encoding="utf-8", make_textclip=make_textclip
            )
            text_clips = []
            for item in sub.subtitles:
                clip = create_text_clip(subtitle_item=item)
                # Filter out clips with invalid dimensions
                if clip and clip.size[0] > 0 and clip.size[1] > 0:
                    text_clips.append(clip)
                else:
                    logger.bind(tag=TAG).warning(f"Skipping invalid text clip: {item}")

            # Only create composite if we have valid text clips
            if text_clips:
                video_clip = CompositeVideoClip([image_clip, *text_clips], size=(video_width, video_height))
            else:
                video_clip = CompositeVideoClip([image_clip], size=(video_width, video_height))
            logger.bind(tag=TAG).info("字幕添加完成")
            # 添加音频
            video_clip = video_clip.with_audio(audio_clip)
            logger.bind(tag=TAG).info("音频添加完成")
            # 输出文件
            logger.bind(tag=TAG).info(f"开始写入视频文件: {video_file_path}")
            video_clip.write_videofile(
                filename=video_file_path,
                fps=self.fps,
                codec=self.video_codec,
                temp_audiofile_path=output_dir,
                audio_codec=self.audio_codec,
                threads=8,
                write_logfile=False,
                ffmpeg_params=self.ffmpeg_params,
                logger='bar'
            )
            logger.bind(tag=TAG).info(f"视频文件写入完成")
            if callback:
                callback(video_file_path)
        except Exception as e:
            logger.bind(tag=TAG).error(f"视频合成过程中出错: {str(e)}")
            raise
        finally:
            # 清理资源
            try:
                if 'video_clip' in locals():
                    self.close_clip(video_clip)
                if 'audio_clip' in locals():
                    self.close_clip(audio_clip)
            except Exception as e:
                logger.bind(tag=TAG).info(f"failed to close clip: {str(e)}")

    def get_bgm_file(self):
        suffix = "*.mp3"
        song_dir = common_utils.song_dir()
        files = glob.glob(os.path.join(song_dir, suffix))
        if files:
            return random.choice(files)
        return ''

    def combine_videos(self, combined_video_path: str, video_paths: List[str],
                       video_aspect: VideoAspect = VideoAspect.portrait):
        """
        按顺序合并视频，添加背景音乐和随机转场效果，合成后上传到服务器
        :param combined_video_path:
        :param video_paths:
        :param video_aspect:
        :return:
        """
        global processed_clips, final_video
        try:
            output_dir = os.path.dirname(combined_video_path)
            # 获取视频尺寸
            aspect = VideoAspect(video_aspect)
            video_width, video_height = aspect.to_resolution()
            logger.bind(tag=TAG).info(f"目标视频尺寸: {video_width}x{video_height}")
            # 加载和处理视频片段
            processed_clips = []
            total_duration = 0
            for i, video_path in enumerate(video_paths):
                logger.bind(tag=TAG).info(f"处理视频 {i + 1}/{len(video_paths)}")
                try:
                    # 加载视频
                    clip = VideoFileClip(video_path)
                    # 调整视频尺寸
                    clip = clip.resized((video_width, video_height))
                    # 应用fadein转场特效
                    clip = self.video_effects.fadein_transition(clip, 1)
                    logger.bind(tag=TAG).info(f"应用随机转场特效")
                    processed_clips.append(clip)
                    total_duration += clip.duration
                    logger.bind(tag=TAG).info(f"视频片段处理完成，时长: {clip.duration:.2f}秒")

                except Exception as e:
                    logger.bind(tag=TAG).info(f"failed to process clip: {str(e)}")
                    continue
            if not processed_clips:
                raise Exception("没有成功处理的视频片段")
            logger.bind(tag=TAG).info(f"总共处理了 {len(processed_clips)} 个视频片段，总时长: {total_duration:.2f}秒")
            # 合并视频
            logger.bind(tag=TAG).info("开始合并视频片段...")
            final_video = concatenate_videoclips(processed_clips, method="compose")
            logger.bind(tag=TAG).info("视频片段合并完成")
            # 随机背景音乐
            bgm_file = self.get_bgm_file()
            bgm_clip = AudioFileClip(bgm_file).with_effects(
                [
                    afx.MultiplyVolume(0.3),  # 背景音乐音量30%
                    afx.AudioFadeOut(3),  # 3秒淡出
                    afx.AudioLoop(duration=final_video.duration),
                ]
            )
            # 合并原音频和背景音乐
            if final_video.audio:
                combined_audio = CompositeAudioClip([final_video.audio, bgm_clip])
            else:
                combined_audio = bgm_clip
            final_video = final_video.with_audio(combined_audio)
            logger.bind(tag=TAG).info("背景音乐添加完成")
            # 清理背景音乐资源
            bgm_clip.close()
            # 输出最终视频
            logger.bind(tag=TAG).info(f"开始写入最终视频: {combined_video_path}")
            final_video.write_videofile(
                filename=combined_video_path,
                fps=self.fps,
                codec=self.video_codec,
                audio_codec=self.audio_codec,
                temp_audiofile_path=output_dir,
                threads=8,
                write_logfile=False,
                ffmpeg_params=self.ffmpeg_params,
                logger='bar'
            )
            logger.bind(tag=TAG).info("视频文件写入完成")

            # 清理视频资源
            for clip in processed_clips:
                self.close_clip(clip)
            self.close_clip(final_video)
            # 上传到服务器
            logger.bind(tag=TAG).info("开始上传视频到服务器...")
            bucket_utils = MinioBucketUtils()

            # 生成唯一的文件名
            file_extension = os.path.splitext(combined_video_path)[1]
            unique_filename = f"combined_video_{uuid.uuid4()}{file_extension}"

            file_url = bucket_utils.local_upload_file(combined_video_path, unique_filename)
            logger.bind(tag=TAG).info(f"视频上传完成: {file_url}")
            # 清理本地临时文件
            try:
                if os.path.exists(combined_video_path):
                    os.remove(combined_video_path)
                    logger.bind(tag=TAG).info(f"清理本地文件: {combined_video_path}")
            except Exception as e:
                logger.bind(tag=TAG).warning(f"清理本地文件失败: {str(e)}")

            return file_url
        except Exception as e:
            logger.bind(tag=TAG).error(f"视频合并过程中出错: {str(e)}")
            raise
        finally:
            # 确保资源清理
            try:
                if 'processed_clips' in locals():
                    for clip in processed_clips:
                        self.close_clip(clip)
                if 'final_video' in locals():
                    self.close_clip(final_video)
            except Exception as e:
                logger.bind(tag=TAG).warning(f"资源清理失败: {str(e)}")

    def close_clip(self, clip):
        if clip is None:
            return

        try:
            # close main resources
            if hasattr(clip, 'reader') and clip.reader is not None:
                clip.reader.close()

            # close audio resources
            if hasattr(clip, 'audio') and clip.audio is not None:
                if hasattr(clip.audio, 'reader') and clip.audio.reader is not None:
                    clip.audio.reader.close()
                del clip.audio

            # close mask resources
            if hasattr(clip, 'mask') and clip.mask is not None:
                if hasattr(clip.mask, 'reader') and clip.mask.reader is not None:
                    clip.mask.reader.close()
                del clip.mask

            # handle child clips in composite clips
            if hasattr(clip, 'clips') and clip.clips:
                for child_clip in clip.clips:
                    if child_clip is not clip:  # avoid possible circular references
                        self.close_clip(child_clip)

            # clear clip list
            if hasattr(clip, 'clips'):
                clip.clips = []

        except Exception as e:
            logger.bind(tag=TAG).info(f"failed to close clip: {str(e)}")

        del clip
        gc.collect()
