from datetime import datetime
from typing import Dict, Any, Optional, List

from sqlalchemy import func
from sqlmodel import Session, select

from app.house_box.house_box_model import PrimeHouseBoxSupplySchema, PrimeHouseBoxCategorySchema
from config import setup_logging
from dependencies import get_session

TAG = __name__
logger = setup_logging()


class BaseService:
    def _get_session(self) -> Session:
        # 每次操作都获取新的session
        return next(get_session())


class PrimeHouseBoxCategoryService(BaseService):

    def create_category(self, category_data: Dict[str, Any]) -> PrimeHouseBoxCategorySchema:
        session = self._get_session()
        try:
            category = PrimeHouseBoxCategorySchema(**category_data)
            session.add(category)
            session.commit()
            session.refresh(category)
            return category
        finally:
            session.close()

    def get_category_by_id(self, category_id: int) -> Optional[PrimeHouseBoxCategorySchema]:
        session = self._get_session()
        try:
            return session.get(PrimeHouseBoxCategorySchema, category_id)
        finally:
            session.close()

    def get_all_categories(self, skip: int = 0, limit: int = 100) -> List[PrimeHouseBoxCategorySchema]:
        session = self._get_session()
        try:
            statement = select(PrimeHouseBoxCategorySchema).offset(skip).limit(limit)
            return session.exec(statement).all()
        finally:
            session.close()

    def get_categories_with_pagination(self, page: int = 1, size: int = 20) -> Dict[str, Any]:
        session = self._get_session()
        try:
            offset = (page - 1) * size
            total_statement = select(func.count(PrimeHouseBoxCategorySchema.id))
            total = session.exec(total_statement).one()

            statement = select(PrimeHouseBoxCategorySchema).offset(offset).limit(size)
            categories = session.exec(statement).all()

            return {
                "records": categories,
                "total": total,
                "page": page,
                "size": size
            }
        finally:
            session.close()

    def update_category(self, category_id: int, category_data: Dict[str, Any]) -> Optional[PrimeHouseBoxCategorySchema]:
        session = self._get_session()
        try:
            category = session.get(PrimeHouseBoxCategorySchema, category_id)
            if category:
                for key, value in category_data.items():
                    if key not in ['id', 'create_time']:
                        setattr(category, key, value)
                category.update_time = datetime.utcnow()
                session.add(category)
                session.commit()
                session.refresh(category)
            return category
        finally:
            session.close()

    def delete_category(self, category_id: int) -> bool:
        session = self._get_session()
        try:
            category = session.get(PrimeHouseBoxCategorySchema, category_id)
            if category:
                session.delete(category)
                session.commit()
                return True
            return False
        finally:
            session.close()


class PrimeHouseBoxSupplyService(BaseService):

    def create_supply(self, supply_data: Dict[str, Any]) -> PrimeHouseBoxSupplySchema:
        session = self._get_session()
        try:
            supply = PrimeHouseBoxSupplySchema(**supply_data)
            session.add(supply)
            session.commit()
            session.refresh(supply)
            return supply
        finally:
            session.close()

    def get_supply_by_id(self, supply_id: int) -> Optional[PrimeHouseBoxSupplySchema]:
        session = self._get_session()
        try:
            return session.get(PrimeHouseBoxSupplySchema, supply_id)
        finally:
            session.close()

    def get_all_supplies(self, skip: int = 0, limit: int = 100) -> List[PrimeHouseBoxSupplySchema]:
        session = self._get_session()
        try:
            statement = select(PrimeHouseBoxSupplySchema).offset(skip).limit(limit)
            return session.exec(statement).all()
        finally:
            session.close()

    def get_supplies_with_pagination(self, page: int = 1, size: int = 20) -> Dict[str, Any]:
        session = self._get_session()
        try:
            offset = (page - 1) * size
            total_statement = select(func.count(PrimeHouseBoxSupplySchema.id))
            total = session.exec(total_statement).one()

            statement = select(PrimeHouseBoxSupplySchema).offset(offset).limit(size)
            supplies = session.exec(statement).all()

            return {
                "records": supplies,
                "total": total,
                "page": page,
                "size": size
            }
        finally:
            session.close()

    def update_supply(self, supply_id: int, supply_data: Dict[str, Any]) -> Optional[PrimeHouseBoxSupplySchema]:
        session = self._get_session()
        try:
            supply = session.get(PrimeHouseBoxSupplySchema, supply_id)
            if supply:
                for key, value in supply_data.items():
                    if key not in ['id', 'create_time']:
                        setattr(supply, key, value)
                supply.update_time = datetime.utcnow()
                session.add(supply)
                session.commit()
                session.refresh(supply)
            return supply
        finally:
            session.close()

    def delete_supply(self, supply_id: int) -> bool:
        session = self._get_session()
        try:
            supply = session.get(PrimeHouseBoxSupplySchema, supply_id)
            if supply:
                session.delete(supply)
                session.commit()
                return True
            return False
        finally:
            session.close()


class HouseBoxService:
    pass
