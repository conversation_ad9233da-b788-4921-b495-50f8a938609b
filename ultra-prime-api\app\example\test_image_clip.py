import random

from moviepy import ImageClip, CompositeVideoClip

from app.clip.effects.factory import create_effect

if __name__ == '__main__':
    image_clip = ImageClip(f'603846251116630016.jpeg', duration=6)
    image_clip = image_clip.resized(height=1080).resized(width=1920)
    effect_list = ['zoom_in', 'ken_burns_left', 'ken_burns_up', 'ken_burns_down']
    ken_burns_effect = create_effect(random.choice(effect_list), start_zoom=1.0, end_zoom=1.2)
    image_clip = ken_burns_effect.apply(image_clip)
    video_clip = CompositeVideoClip([image_clip], size=(1920, 1080))
    video_clip.write_videofile(
        filename='out.mp4',
        fps=30,
        codec='h264_nvenc',
        temp_audiofile_path='/',
        audio_codec='aac',
        threads=8,
        write_logfile=False,
        ffmpeg_params=[
            '-y',
        ],
        logger='bar'
    )
