from fastapi import APIRouter

from app.generator.generator_service import GeneratorService
from config import setup_logging

router = APIRouter(prefix="/generator", tags=["generator代码生成器"])

TAG = __name__
logger = setup_logging()

generator_service = GeneratorService()


@router.post("/create_table_with_ai")
async def create_table_with_ai(create_table: dict):
    return generator_service.create_table_with_ai(create_table)


@router.post("/code_generator_variable")
async def code_generator_variable(gen_table: dict):
    return generator_service.code_generator_variable(gen_table)
