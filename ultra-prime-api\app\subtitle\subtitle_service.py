from faster_whisper import WhisperModel

from config import setup_logging

TAG = __name__
logger = setup_logging()


class SubtitleService:
    def __init__(self):
        self.model_size_or_path = 'large-v3'

    def format_timestamp(self, seconds: float) -> str:
        """将秒转换为 SRT 时间格式 HH:MM:SS,mmm"""
        ms = int((seconds - int(seconds)) * 1000)
        s = int(seconds)
        h, s = divmod(s, 3600)
        m, s = divmod(s, 60)
        return f"{h:02}:{m:02}:{s:02},{ms:03}"

    def generate_srt(self, audio_file, output_srt="subtitle.srt", language="zh", device="auto"):
        # 1. 加载模型
        logger.bind(tag=TAG).info(f"Loading model: {self.model_size_or_path} on {device}...")
        model = WhisperModel(self.model_size_or_path, device=device,
                             compute_type="float16" if device == "cuda" else "int8")

        # 2. 转录
        logger.bind(tag=TAG).info("Transcribing audio...")
        segments, info = model.transcribe(
            audio_file,
            language=language,
            beam_size=3,
            word_timestamps=True
        )

        # 将生成器转为列表以便多次使用
        segments = list(segments)

        # 3. 写入 SRT 文件
        with open(output_srt, "w", encoding="utf-8") as f:
            for i, seg in enumerate(segments, start=1):
                start = self.format_timestamp(seg.start)
                end = self.format_timestamp(seg.end)
                text = seg.text.strip()
                f.write(f"{i}\n{start} --> {end}\n{text}\n\n")

        logger.bind(tag=TAG).info(f"✅ 字幕已保存至: {output_srt}")
        logger.bind(tag=TAG).info(f"检测语言: {info.language}, 概率: {info.language_probability:.2f}")
        return output_srt
