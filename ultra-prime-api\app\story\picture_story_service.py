import os
import time
from datetime import datetime

from sqlalchemy import null
from sqlmodel import select

from app.image.image_model import ImageGenerationRequest
from app.image.image_service import ImageOpenAIService
from app.story.story_model import PictureStoryRoleSchema, PictureStorySceneSchema, PictureStorySchema
from app.story.story_service import StoryService
from app.util import common_utils, const
from app.util.generator_executor import BlockingBoundedExecutor
from app.util.state_manager import RedisStateManager
from app.clip.picture_story_clip_service import PictureStoryClipService
from app.video.video_model import VideoAspect
from app.voice.voice_service import VoiceEdgeTtsService
from app.writer.writer_service import WriterService
from config import setup_logging, Config

TAG = __name__
logger = setup_logging()

# 创建线程池用于异步任务
executor = BlockingBoundedExecutor(max_workers=2, max_queue=10)


class PictureStoryService(StoryService):

    def picture_story_analysis_content_status(self, story_id: str):
        """
        获取绘本故事内容分析状态
        """
        state_manager = RedisStateManager(story_id)
        task = state_manager.get_task()
        if task:
            return {
                'state': str(task['state']),
                'progress': int(task['progress'])
            }
        else:
            return {
                'state': '1',
                'progress': 0
            }

    def get_picture_story_detail(self, story_id: str):
        session = self._get_session()
        try:
            return session.get(PictureStorySchema, story_id)
        finally:
            session.close()

    def _process_story_analysis_content_async(self, story_id: str, existing_roles: str, novel_content: str,
                                              state_manager):
        """
        异步处理绘本故事内容分析
        """
        session = self._get_session()
        writer_service = WriterService()
        try:
            logger.bind(tag=TAG).info(f"开始生成绘本故事")
            pss = writer_service.generate_picture_story_shot(existing_roles, novel_content)
            state_manager.update_task(progress=50)
            # 新增镜头场景
            for scene in pss.scenes:
                scene_schema = PictureStorySceneSchema(
                    story_id=story_id,
                    scene_name=scene.scene_name,
                    role_names=scene.role_names,
                    narration=scene.narration,
                    scene_description=scene.scene_description,
                    scene_prompt=scene.scene_prompt
                )
                session.add(scene_schema)
                session.commit()
            # 新增角色
            for role in pss.roles:
                role_schema = PictureStoryRoleSchema(
                    story_id=story_id,
                    role_name=role.role_name,
                    role_type=role.role_type,
                    role_description=role.role_description,
                    role_prompt=role.role_prompt
                )
                session.add(role_schema)
                session.commit()
            logger.bind(tag=TAG).info(f"绘本故事分析完成->story_id: {story_id}")
            state_manager.update_task(progress=100, state=const.TASK_STATE_COMPLETE)
        except Exception as e:
            logger.bind(tag=TAG).opt(exception=True).error(e)
            state_manager.update_task(state=const.TASK_STATE_FAILED)
        finally:
            session.close()

    def _generator_role_image(self, role_schema: PictureStoryRoleSchema, video_aspect: str, task_id: str):
        try:
            image_service = ImageOpenAIService()
            image_url = image_service.generate_image_async(ImageGenerationRequest(
                prompt=role_schema.role_prompt,
                negative_prompt=role_schema.role_negative_prompt,
                image_size=video_aspect,
                image_style="default"
            ))
            if not image_url:
                raise Exception('生成图失败')
            session = self._get_session()
            try:
                # 保存到本地路径
                common_utils.download_web_image(image_url,
                                                os.path.join(common_utils.task_dir(str(task_id) + '/roles'),
                                                             f"{role_schema.id}.png"))
                role_schema.role_image_url = image_url
                role_schema.update_time = datetime.now()
                session.add(role_schema)
                session.commit()
                logger.bind(tag=TAG).info(f"生成角色图片完成")
            except Exception as e:
                logger.bind(tag=TAG).opt(exception=True).error(f"生成角色图片失败: {str(e)}")
            finally:
                session.close()
        except Exception as e:
            logger.bind(tag=TAG).opt(exception=True).error(e)

    def _update_scene_video_status(self, scene_id: str, status: str = '1'):
        logger.bind(tag=TAG).info(f'scene_id={scene_id}, status={status}')
        session = self._get_session()
        try:
            scene = session.get(PictureStorySchema, scene_id)
            if scene:
                scene.scene_video_status = status
                scene.update_time = datetime.now()
                session.add(scene)
                session.commit()
        finally:
            session.close()

    def _generator_scene_image(self, scene_schema: PictureStorySceneSchema, image: str, video_aspect: str,
                               task_id: str):
        try:
            image_service = ImageOpenAIService()
            image_url = image_service.generate_image_async(
                ImageGenerationRequest(
                    prompt=scene_schema.scene_prompt,
                    negative_prompt=scene_schema.scene_negative_prompt,
                    image_size=video_aspect,
                    image_style="default",
                    image=image
                )
            )
            if not image_url:
                raise Exception('生成图失败')
            session = self._get_session()
            try:
                # 保存到本地路径
                common_utils.download_web_image(image_url,
                                                os.path.join(common_utils.task_dir(str(task_id) + '/scenes'),
                                                             f"{scene_schema.id}.png"))
                scene_schema.scene_image_url = image_url
                scene_schema.update_time = datetime.now()
                session.add(scene_schema)
                session.commit()
                logger.bind(tag=TAG).info(f"生成场景图片完成")
            except Exception as e:
                logger.bind(tag=TAG).opt(exception=True).error(f"生成场景图片失败: {str(e)}")
            finally:
                session.close()
        except Exception as e:
            logger.bind(tag=TAG).opt(exception=True).error(e)

    def analysis_content_of_the_novel(self, story_id: str, novel_title: str, novel_content: str,
                                      video_aspect: str, voice_name: str, user_id: str):
        session = self._get_session()
        existing_roles = ''
        try:
            # 查询已有的角色
            if story_id:
                statement = select(PictureStoryRoleSchema).where(
                    PictureStoryRoleSchema.story_id == story_id
                )
                roles = session.exec(statement).all()
                existing_roles = ','.join([role.role_name for role in roles])
                logger.bind(tag=TAG).info(f"查询已有的角色->existing_roles: {existing_roles}")
            else:
                # 新增故事
                picture_story = PictureStorySchema(
                    user_id=user_id,
                    story_title=novel_title,
                    story_content=novel_content,
                    voice_name=voice_name,
                    video_aspect=video_aspect
                )
                session.add(picture_story)
                session.commit()
                story_id = picture_story.id
                logger.bind(tag=TAG).info(f"新增绘本故事->story_id: {story_id}")
            state_manager = RedisStateManager(story_id)
            # 启动并提交异步任务
            executor.submit(self._process_story_analysis_content_async, story_id, existing_roles, novel_content,
                            state_manager)
        finally:
            session.close()

    def generate_images_based_on_the_scene(self, story_id: str, scene_id: str):
        """
        根据场景生成图片
        :param story_id: 绘本故事ID
        :param scene_id: 场景ID
        :return:
        """
        session = self._get_session()
        try:
            # 查询故事
            story = session.get(PictureStorySchema, story_id)
            if not story:
                logger.bind(tag=TAG).error(f"未查询到绘本故事")
                return
            # 查询场景
            statement = select(PictureStorySceneSchema).where(
                PictureStorySceneSchema.story_id == story_id
            )
            if scene_id:
                logger.bind(tag=TAG).info(f"单独生成图: {scene_id}")
                statement = statement.where(PictureStorySceneSchema.id == scene_id)
            else:
                statement = statement.where(PictureStorySceneSchema.scene_image_url == null())
            scenes = session.exec(statement).all()
            logger.bind(tag=TAG).info(f'场景数量：{len(scenes)}')
            if scenes:
                for scene in scenes:
                    logger.bind(tag=TAG).info(f"生成图片-提交生图任务->scene: {scene.scene_prompt}")
                    # 根据场景的角色查询角色参考图
                    role_schema = session.exec(select(PictureStoryRoleSchema).where(
                        PictureStoryRoleSchema.story_id == story_id,
                        # 目前参考图只支持1个
                        PictureStoryRoleSchema.role_name == scene.role_names.split(',')[0]
                    )).first()
                    image = None
                    if role_schema:
                        # 转为base64
                        image = common_utils.file_to_base64(
                            os.path.join(Config.APP_TMP_STORAGE, str(story.task_id), "roles", f"{role_schema.id}.png"))
                    time.sleep(1)
                    executor.submit(self._generator_scene_image, scene, image, story.video_aspect, story.task_id)
            else:
                logger.bind(tag=TAG).error(f"未查询到场景")
        finally:
            session.close()

    def get_picture_story_scenes(self, story_id: str, scene_video_status: str = None):
        session = self._get_session()
        try:
            statement = select(PictureStorySceneSchema).where(
                PictureStorySceneSchema.story_id == story_id
            )
            if scene_video_status:
                statement = statement.where(PictureStorySceneSchema.scene_video_status == scene_video_status)
            return session.exec(statement).all()
        finally:
            session.close()

    def generate_images_based_on_the_role(self, story_id: str, role_id: str):
        """
        根据角色生成图片
        :param story_id: 绘本故事ID
        :param role_id: 角色ID
        :return:
        """
        session = self._get_session()
        try:
            # 查询故事
            story = session.get(PictureStorySchema, story_id)
            if not story:
                logger.bind(tag=TAG).error(f"未查询到绘本故事")
                return
            # 查询角色
            statement = select(PictureStoryRoleSchema).where(
                PictureStoryRoleSchema.story_id == story_id
            )
            if role_id:
                logger.bind(tag=TAG).info(f"单独生成图: {role_id}")
                statement = statement.where(PictureStoryRoleSchema.id == role_id)
            else:
                statement = statement.where(PictureStoryRoleSchema.role_image_url == null())
            roles = session.exec(statement).all()
            if roles:
                for role in roles:
                    logger.bind(tag=TAG).info(f"生成图片-提交生图任务->role: {role.role_prompt}")
                    time.sleep(1)
                    executor.submit(self._generator_role_image, role, "9:16", story.task_id)
            else:
                logger.bind(tag=TAG).error(f"未查询到角色")
        finally:
            session.close()

    def get_picture_story_roles(self, story_id: str):
        session = self._get_session()
        try:
            statement = select(PictureStoryRoleSchema).where(
                PictureStoryRoleSchema.story_id == story_id
            )
            return session.exec(statement).all()
        finally:
            session.close()

    async def generate_audio_and_subtitle(self, story_id: str, voice_name: str):
        """
        生成音频和字幕
        :param voice_name:  voice_name
        :param story_id: story_id
        :return:
        """
        session = self._get_session()
        try:
            story = session.get(PictureStorySchema, story_id)
            if not story:
                logger.bind(tag=TAG).error(f"未查询到绘本故事")
                raise Exception("未查询到绘本故事")
            scenes = self.get_picture_story_scenes(story_id)
            voice_service = VoiceEdgeTtsService()
            for scene in scenes:
                # 生成音频和字幕
                voice_file = os.path.join(common_utils.task_dir(story.task_id), f'scenes/{scene.id}.mp3')
                subtitle_file = os.path.join(common_utils.task_dir(story.task_id), f'scenes/{scene.id}.srt')
                logger.bind(tag=TAG).info(f"{scene.id}-开始生成音频和字幕")
                voice_duration = await voice_service.speech_synthesis_local(
                    speech_text=scene.narration,
                    audio_file_path=voice_file,
                    subtitle_file_path=subtitle_file,
                    voice=voice_name
                )
                logger.bind(tag=TAG).info(f"{scene.id}-生成音频和字幕完成，{voice_duration}")
            story.voice_name = voice_name
            # 可保存文件信息
            session.add(story)
            session.commit()
        finally:
            session.close()

    async def generate_scene_video(self, story_id: str):
        scenes = self.get_picture_story_scenes(story_id, scene_video_status='0')
        picture_story_clip_service = PictureStoryClipService()
        story = self.get_picture_story_detail(story_id)
        if not story:
            raise Exception("未查询到绘本故事")

        session = self._get_session()
        try:
            for scene in scenes:
                image_file_path = os.path.join(common_utils.task_dir(story.task_id), f'scenes/{scene.id}.png')
                voice_file_path = os.path.join(common_utils.task_dir(story.task_id), f'scenes/{scene.id}.mp3')
                subtitle_file_path = os.path.join(common_utils.task_dir(story.task_id), f'scenes/{scene.id}.srt')
                video_file_path = os.path.join(common_utils.task_dir(story.task_id), f'scenes/{scene.id}.mp4')
                video_aspect = story.video_aspect
                logger.bind(tag=TAG).info(f"{scene.id}-开始合成视频")
                picture_story_clip_service.composite_video(image_file_path, voice_file_path, subtitle_file_path,
                                                           video_file_path,
                                                           VideoAspect.portrait if video_aspect == '9:16' else VideoAspect.landscape,
                                                           callback=self._update_scene_video_status)
                # 生成进度
                scene.update_time = datetime.now()
                session.add(scene)
                session.commit()

            logger.bind(tag=TAG).info(f"绘本故事视频合成完成")
        finally:
            session.close()

    def composite_video(self, story_id: str):
        """
        合成视频
        :param story_id:
        :return:
        """
        session = self._get_session()
        try:
            story = session.get(PictureStorySchema, story_id)
            if not story:
                logger.bind(tag=TAG).error(f"未查询到绘本故事")
                raise Exception("未查询到绘本故事")
            if story.final_picture_url:
                logger.bind(tag=TAG).info(f"已合成视频")
                raise Exception("已合成视频")
            # 合成视频
            picture_story_clip_service = PictureStoryClipService()
            combined_video_path = os.path.join(common_utils.task_dir(story.task_id), "combined-video.mp4")
            logger.bind(tag=TAG).info(f"开始合成视频")
            scenes_dir = os.path.join(common_utils.task_dir(story.task_id), "scenes")
            video_paths = []
            if os.path.exists(scenes_dir) and os.path.isdir(scenes_dir):
                video_paths = [
                    os.path.join(scenes_dir, f)
                    for f in sorted(os.listdir(scenes_dir))
                    if os.path.isfile(os.path.join(scenes_dir, f)) and f.lower().endswith('.mp4')
                ]

            file_url = picture_story_clip_service.combine_videos(combined_video_path,
                                                         video_paths,
                                                         VideoAspect.landscape if story.video_aspect == '16:9' else VideoAspect.portrait)
            story.final_picture_url = file_url
            session.add(story)
            session.commit()
        finally:
            session.close()

    def picture_story_my_video_work(self, user_id: str):
        """
        获取用户的绘本故事作品列表
        :return: 用户作品列表
        """
        session = self._get_session()
        try:
            statement = select(PictureStorySchema).where(
                PictureStorySchema.user_id == user_id
            ).order_by(PictureStorySchema.create_time.desc())
            stories = session.exec(statement).all()
            stories_response = []
            for story in stories:
                stories_response.append({
                    "id": str(story.id),
                    "story_title": story.story_title,
                    "story_content": story.story_content[:100] + "..." if len(
                        story.story_content) > 100 else story.story_content,
                    "video_aspect": story.video_aspect,
                    "final_picture_url": story.final_picture_url,
                    "thumbnail": f"https://dummyimage.com/320x180/4CAF50/fff&text={story.story_title[:10]}",
                    "create_time": story.create_time.strftime("%Y-%m-%d %H:%M:%S")
                })
            return stories_response
        finally:
            session.close()
