import json
import time
from abc import abstractmethod, ABC

import requests

from app.util import common_utils, const
from app.util.bucket_utils import MinioBucketUtils
from app.video.video_model import VideoGenerationRequest
from config import Config, setup_logging

TAG = __name__
logger = setup_logging()


class VideoBaseService(ABC):
    def __init__(self):
        pass

    @abstractmethod
    def submit_video_task(self, request: VideoGenerationRequest, state_manager):
        pass


class DashscopeVideoService(VideoBaseService):
    def submit_video_task(self, request: VideoGenerationRequest, state_manager):
        # 1. 提交任务
        image_base64 = common_utils.file_to_base64(request.image_path)
        payload = {
            "model": Config.DASHSCOPE_VIDEO_MODEL,
            "input": {
                "img_url": f"data:image/png;base64,{image_base64}",
                "prompt": request.prompt
            },
            "parameters": {
                "resolution": "1080P",
                "duration": 5,
                "prompt_extend": True,
                "watermark": False
            }
        }
        if request.negative_prompt:
            payload["input"]["negative_prompt"] = request.negative_prompt
        headers = {
            "Authorization": f"Bearer {Config.DASHSCOPE_API_KEY}",
            "Content-Type": "application/json",
            "X-DashScope-Async": "enable"
        }
        try:
            state_manager.update_task(progress=20)
            resp = requests.post(Config.DASHSCOPE_VIDEO_TASK_URL, headers=headers, data=json.dumps(payload))
            logger.bind(tag=TAG).info(resp.json())
            resp.raise_for_status()

            task_id = resp.json()["output"]["task_id"]
            logger.bind(tag=TAG).info(f"task_id: {task_id}")
            state_manager.update_task(progress=40)
            # 2. 轮询任务状态（约 20-60 秒）
            while True:
                r = requests.get(Config.DASHSCOPE_VIDEO_TASK_QUERY_URL + task_id,
                                 headers={"Authorization": headers["Authorization"]})
                status = r.json()["output"]["task_status"]
                if status == "SUCCEEDED":
                    video_url = r.json()["output"]["video_url"]
                    logger.bind(tag=TAG).info("生成完成:", video_url)
                    state_manager.update_task(progress=55)
                    # 上传到服务器
                    minio_bucket_utils = MinioBucketUtils()
                    image_url = minio_bucket_utils.convert_to_bucket_url(video_url, ext='.mp4')
                    state_manager.update_task(progress=100, state=const.TASK_STATE_COMPLETE)
                    return image_url
                elif status in ("FAILED", "CANCELED"):
                    state_manager.update_task(state=const.TASK_STATE_FAILED)
                    raise RuntimeError("任务失败", r.text)
                time.sleep(15)
        except Exception as e:
            logger.bind(tag=TAG).opt(exception=True).error(e)
            state_manager.update_task(state=const.TASK_STATE_FAILED)
