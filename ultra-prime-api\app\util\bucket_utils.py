import io
import os
import uuid
from abc import abstractmethod, ABC

import fake_useragent
import oss2
import requests
from fastapi import UploadFile
from minio import Minio

from config import Config, setup_logging

TAG = __name__
logger = setup_logging()


class BucketUtils(ABC):
    def __init__(self):
        self.headers = {
            "User-Agent": fake_useragent.UserAgent(os=["Windows"]).random,
            "Accept": "image/webp,image/apng,image/*,*/*;q=0.8",
        }

    def _get_extension_from_content_type(self, content_type: str) -> str:
        """
        根据content-type获取文件扩展名
        """
        mime_to_ext = {
            'image/jpeg': '.jpg',
            'image/png': '.png',
            'image/gif': '.gif',
            'image/webp': '.webp',
            'application/pdf': '.pdf',
            'text/plain': '.txt',
            'application/zip': '.zip',
            'application/x-zip-compressed': '.zip'
        }
        return mime_to_ext.get(content_type.lower(), '.bin')

    @abstractmethod
    def convert_to_bucket_url(self, web_url: str):
        pass

    @abstractmethod
    def local_upload_file(self, file_path: str, object_name: str = None) -> str:
        pass

    @abstractmethod
    def form_upload_file(self, file: UploadFile):
        pass


class MinioBucketUtils(BucketUtils):

    def __init__(self):
        super().__init__()
        self.client = Minio(
            endpoint=Config.MINIO_ENDPOINT,
            access_key=Config.MINIO_ACCESS_KEY,
            secret_key=Config.MINIO_SECRET_KEY,
            secure=False
        )
        self.bucket_name = Config.MINIO_BUCKET_NAME
        if not self.client.bucket_exists(self.bucket_name):
            self.client.make_bucket(self.bucket_name)
        self.domain = f'http://{Config.MINIO_ENDPOINT}/{Config.MINIO_BUCKET_NAME}'
        logger.bind(tag=TAG).info("MinIO 连接初始化成功")

    def convert_to_bucket_url(self, web_url: str, ext: str = '.png') -> str:
        try:
            # Step 1: 下载文件流
            response = requests.get(web_url, stream=True, headers=self.headers)
            response.raise_for_status()

            # Step 5: 生成随机文件名
            filename = f"source/{uuid.uuid4()}{ext}"

            # Step 6: 将内容读入内存流
            file_stream = io.BytesIO()
            for chunk in response.iter_content(chunk_size=1024 * 1024):
                if chunk:
                    file_stream.write(chunk)
            file_stream.seek(0)  # 重置流指针到开头

            # Step 7: 直接上传内存流到 MinIO
            self.client.put_object(
                self.bucket_name,
                filename,
                data=file_stream,
                length=file_stream.getbuffer().nbytes,
                content_type=response.headers.get('Content-Type', 'application/octet-stream')
            )

            # Step 8: 构造并返回新 URL
            minio_url = f"{self.domain}/{filename}"
            logger.bind(tag=TAG).info(f"转换后的url={minio_url}")
            return minio_url

        except Exception as e:
            logger.bind(tag=TAG).error(f"上传文件失败: {e}")
            return None

    def local_upload_file(self, file_path: str, object_name: str = None) -> str:
        """
        上传本地文件到 Minio，返回文件的访问URL
        :param file_path: 本地文件路径
        :param object_name: 存储到 Minio 的对象名（可选，默认用文件名）
        :return: 文件URL
        """
        if not object_name:
            object_name = os.path.basename(file_path)
        # 上传
        self.client.fput_object(self.bucket_name, object_name, file_path)
        # 构造外链
        return f"{self.domain}/{object_name}"

    def form_upload_file(self, file: UploadFile):
        try:
            # 生成随机文件名，保留原始扩展名
            ext = os.path.splitext(file.filename)[1] if file.filename else ".bin"
            object_name = f"uploads/{uuid.uuid4()}{ext}"
            # 获取文件内容
            contents = file.file.read()
            # 使用 put_object 上传文件流
            self.client.put_object(
                bucket_name=self.bucket_name,
                object_name=object_name,
                data=io.BytesIO(contents),
                length=len(contents),
                content_type=file.content_type or "application/octet-stream"
            )
            # 构造并返回可访问的 URL
            url = f"{self.domain}/{object_name}"
            logger.bind(tag=TAG).info(f"表单文件上传成功，URL: {url}")
            return url
        except Exception as e:
            logger.bind(tag=TAG).error(f"表单文件上传失败: {e}")
            return None


class AliOssBucketUtils(BucketUtils):
    def __init__(self):
        super().__init__()
        self.auth = oss2.Auth(Config.OSS_ACCESS_KEY, Config.OSS_SECRET_KEY)
        self.bucket = oss2.Bucket(self.auth, Config.OSS_END_POINT, Config.OSS_BUCKET_NAME)
        # 构造外链域名
        self.domain = f'https://{Config.OSS_BUCKET_NAME}.{Config.OSS_END_POINT}'
        logger.bind(tag=TAG).info("阿里云OSS连接初始化成功")

    def convert_to_bucket_url(self, web_url: str):
        try:
            # 下载文件流
            response = requests.get(web_url, stream=True, headers=self.headers)
            response.raise_for_status()

            # 获取文件扩展名
            content_type = response.headers.get('Content-Type', '')
            ext = self._get_extension_from_content_type(content_type)

            # 生成随机文件名
            object_name = f"source/{uuid.uuid4()}{ext}"

            # 读取文件内容
            file_content = io.BytesIO()
            for chunk in response.iter_content(chunk_size=1024 * 1024):
                if chunk:
                    file_content.write(chunk)
            file_content.seek(0)

            # 上传到OSS
            self.bucket.put_object(object_name, file_content)

            # 构造并返回URL
            oss_url = f"{self.domain}/{object_name}"
            logger.bind(tag=TAG).info(f"转换后的url={oss_url}")
            return oss_url

        except Exception as e:
            logger.bind(tag=TAG).error(f"上传文件失败: {e}")
            return None

    def local_upload_file(self, file_path: str, object_name: str = None) -> str:
        """
               上传本地文件到阿里云OSS，返回文件的访问URL
               :param file_path: 本地文件路径
               :param object_name: 存储到OSS的对象名（可选，默认用文件名）
               :return: 文件URL
               """
        try:
            if not object_name:
                object_name = f"uploads/{os.path.basename(file_path)}"

            # 上传文件
            self.bucket.put_object_from_file(object_name, file_path)

            # 构造外链
            url = f"{self.domain}/{object_name}"
            logger.bind(tag=TAG).info(f"本地文件上传成功，URL: {url}")
            return url

        except Exception as e:
            logger.bind(tag=TAG).error(f"本地文件上传失败: {e}")
            return None

    def form_upload_file(self, file: UploadFile):
        """
               上传FastAPI表单文件到阿里云OSS，返回文件的访问URL
               :param file: FastAPI的UploadFile对象
               :return: 文件URL
               """
        try:
            # 生成随机文件名，保留原始扩展名
            ext = os.path.splitext(file.filename)[1] if file.filename else ".bin"
            object_name = f"uploads/{uuid.uuid4()}{ext}"

            # 读取文件内容
            contents = file.file.read()

            # 上传到OSS
            self.bucket.put_object(object_name, io.BytesIO(contents))

            # 构造并返回URL
            url = f"{self.domain}/{object_name}"
            logger.bind(tag=TAG).info(f"表单文件上传成功，URL: {url}")
            return url

        except Exception as e:
            logger.bind(tag=TAG).error(f"表单文件上传失败: {e}")
            return None
