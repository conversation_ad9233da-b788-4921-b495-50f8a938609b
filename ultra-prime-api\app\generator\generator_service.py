from app.generator.generator_model import CodeTplVarResultSchema
from app.generator.generator_prompt import create_schema_sys_prompt, create_tpl_var_sys_prompt, \
    create_tpl_var_user_prompt, create_schema_user_prompt
from app.util.base_model import ApiResponse
from app.util.llm import llm_chain, structured_llm_chain
from config import setup_logging

TAG = __name__
logger = setup_logging()


class GeneratorService:
    def create_table_with_ai(self, create_table):
        response = llm_chain(sys_prompt=create_schema_sys_prompt,
                             user_prompt=create_schema_user_prompt.format(
                                 table_name=create_table['table_name'],
                                 table_desc=create_table['table_desc'],
                                 user_guide=create_table['user_guide']
                             ))
        return ApiResponse(data=response)

    def code_generator_variable(self, gen_table: dict):
        logger.bind(tag=TAG).info(f"table_name={gen_table['table_name']}，table_comment={gen_table['table_comment']}")
        logger.bind(tag=TAG).info(f"table_fields={gen_table['table_fields']}")
        response = structured_llm_chain(
            sys_prompt=create_tpl_var_sys_prompt,
            user_prompt=create_tpl_var_user_prompt.format(
                table_name=gen_table['table_name'],
                table_comment=gen_table['table_comment'],
                table_fields=gen_table['table_fields']
            ),
            schema=CodeTplVarResultSchema
        )
        return ApiResponse(data=response)
