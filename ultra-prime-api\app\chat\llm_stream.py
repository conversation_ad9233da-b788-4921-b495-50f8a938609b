import json
import time
import uuid
from typing import List

from langchain_core.messages import BaseMessage

from app.chat.chat_servce import ChatService
from app.util.llm import get_llm
from config import Config

chat_service = ChatService()


def llm_stream_chat(messages: List[BaseMessage], agent_id: str = None, user_id: str = None):
    chunk_id = f"chatcmpl-{uuid.uuid4().hex[:16]}"
    created = int(time.time())
    idx = 0

    # 如果有 agent_id 和 user_id，收集完整回复用于保存
    complete_response = ""
    should_save_history = agent_id is not None and user_id is not None

    for chunk in get_llm().stream(messages):
        if hasattr(chunk, 'content') and chunk.content:
            # 收集完整回复内容
            if should_save_history:
                complete_response += chunk.content

            data = {
                "id": chunk_id,
                "object": "chat.completion.chunk",
                "created": created,
                "model": Config.OPENAI_CHAT_MODEL,
                "choices": [
                    {
                        "index": 0,
                        "delta": {
                            "role": "assistant" if idx == 0 else None,
                            "content": chunk.content
                        },
                        "finish_reason": None
                    }
                ]
            }
            idx += 1
            yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"

    # 保存完整的 AI 回复到历史记录
    if should_save_history and complete_response:
        chat_service.save_history(user_id, agent_id, 'assistant', complete_response)

    # 结束信号
    yield "data: [DONE]\n\n"
