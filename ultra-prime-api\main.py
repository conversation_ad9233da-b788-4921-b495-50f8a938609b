from fastapi import FastAP<PERSON>, Request
from starlette.middleware.cors import CORSMiddleware
from starlette.responses import JSONResponse

from app.chat import chat_router
from app.common import common_router
from app.generator import generator_router
from app.house_box import house_box_router
from app.image import image_router
from app.novel import the_legend_novel_router
from app.story import story_router
from app.util.base_model import ApiResponse
from app.video import video_router
from app.voice import voice_router
from app.writer import writer_router
from config import setup_logging

TAG = __name__
logger = setup_logging()

app = FastAPI()


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.bind(tag=TAG).info(f"request: {request}")
    logger.bind(tag=TAG).error(f"全局异常日志: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content=ApiResponse(code=500, description=f"服务器内部错误: {str(exc)}").model_dump()
    )


# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],  # 允许所有方法
    allow_headers=["*"],  # 允许所有头
)

# 注册API路由
app.include_router(common_router.router)
app.include_router(generator_router.router)
app.include_router(the_legend_novel_router.router)
app.include_router(story_router.router)
app.include_router(writer_router.router)
app.include_router(chat_router.router)
app.include_router(image_router.router)
app.include_router(voice_router.router)
app.include_router(video_router.router)
app.include_router(house_box_router.router)
