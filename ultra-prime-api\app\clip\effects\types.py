from typing import Literal

from app.clip.effects.crossfade import CrossFadeInEffect, CrossFadeOutEffect
from app.clip.effects.fade import FadeInEffect, FadeOutEffect
from app.clip.effects.pan import PanLeftEffect, PanRightEffect, PanUpEffect, PanDownEffect
from app.clip.effects.zoom import ZoomInEffect, ZoomOutEffect

VideoEffect = (
    ZoomInEffect
    | ZoomOutEffect
    | PanLeftEffect
    | PanRightEffect
    | PanUpEffect
    | PanDownEffect
    | FadeInEffect
    | FadeOutEffect
    | CrossFadeInEffect
    | CrossFadeOutEffect
)
"""A type representing any video effect."""

VideoEffectType = Literal[
    "zoom_in",
    "zoom_out",
    "pan_left",
    "pan_right",
    "pan_up",
    "pan_down",
    "fade_in",
    "fade_out",
    "crossfade_out",
    "crossfade_in",
]
"""A type representing the type of a video effect."""