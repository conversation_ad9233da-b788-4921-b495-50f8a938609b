import uuid

from langchain_core.messages import SystemMessage, HumanMessage

from app.novel.novel_llm import llm_novel_stream
from app.novel.novel_model import NovelBasicInfo, NovelEditParams
from app.novel.novel_service import NovelService
from app.novel.the_legend_novel_prompt import the_legend_core_seed_prompt, the_legend_character_dynamics_prompt, \
    the_legend_world_building_prompt, the_legend_plot_architecture_prompt, the_legend_chapter_blueprint_prompt, \
    the_legend_first_chapter_prompt, the_legend_next_chapter_prompt, the_legend_summary_update_prompt, \
    the_legend_character_update_prompt
from app.util.bucket_utils import MinioBucketUtils
from app.util.llm import llm_chain
from config import setup_logging

TAG = __name__
logger = setup_logging()

novel_service = NovelService()


class NovelService:
    def upload_novel_cover(self, novel_id: str, novel_cover_file):
        file_bucket = MinioBucketUtils()
        file_url = file_bucket.form_upload_file(novel_cover_file)
        # 更新小说封面
        novel_basic_info = NovelBasicInfo(
            novel_id=novel_id,
            novel_cover=file_url
        )
        novel_service.save_novel_basic_info(novel_basic_info)

    def get_user_novel_work(self, user_id: str):
        return novel_service.get_user_novel_work(user_id)

    def remove_user_novel_work(self, novel_id: str, user_id: str):
        return novel_service.remove_user_novel_work(novel_id, user_id)


class TheLegendNovelService(NovelService):

    def get_novel_basic_info(self, novel_id: str, user_id: str):
        """
        获取小说基本信息
        :param user_id:
        :param novel_id: 小说id
        :return:
        """
        return novel_service.get_novel_basic_info(novel_id, user_id)

    def save_novel_basic_info(self, novel_basic_info: NovelBasicInfo):
        if novel_basic_info.novel_id is None or novel_basic_info.novel_id == "":
            novel_basic_info.novel_id = str(uuid.uuid4())
        novel_service.save_novel_basic_info(novel_basic_info)

    def novel_creation_params_query(self, novel_id, novel_param):
        """
        查询小说创作参数内容
        :param novel_id: 小说id
        :param novel_param: 小说参数
        :return:
        """
        return novel_service.novel_creation_params_query(novel_id, novel_param)

    def novel_creation_params_generator(self, novel_id: str, novel_param: str):
        """
        使用大模型创作小说参数
        :param novel_id: 小说id
        :param novel_param: 小说参数
        :return:
        """
        messages = []
        if novel_param == 'core_seed':
            messages = [
                SystemMessage(content="你是一个十年以上的传奇游戏老玩家，精通传奇游戏世界设定。"),
                HumanMessage(content=the_legend_core_seed_prompt)
            ]

        elif novel_param == 'character_dynamics':
            # 角色动力学
            core_seed = self.novel_creation_params_query(novel_id, 'core_seed')
            if core_seed:
                messages = [
                    SystemMessage(content="你是一个十年以上的传奇游戏老玩家，精通传奇游戏世界设定。"),
                    HumanMessage(content=the_legend_character_dynamics_prompt.format(
                        core_seed=core_seed,
                        user_guidance=''
                    ))
                ]
            else:
                raise Exception('请先生成核心种子')
        elif novel_param == 'world_building':
            # 世界构建矩阵
            core_seed = self.novel_creation_params_query(novel_id, 'core_seed')
            if core_seed:
                messages = [
                    SystemMessage(content="你是一个十年以上的传奇游戏老玩家，精通传奇游戏世界设定。"),
                    HumanMessage(content=the_legend_world_building_prompt.format(
                        core_seed=core_seed,
                        user_guidance=''
                    ))
                ]
            else:
                raise Exception('请先生成核心种子')
        elif novel_param == 'plot_architecture':
            # 情节架构
            core_seed = self.novel_creation_params_query(novel_id, 'core_seed')
            character_dynamics = self.novel_creation_params_query(novel_id, 'character_dynamics')
            world_building = self.novel_creation_params_query(novel_id, 'world_building')
            if core_seed and character_dynamics and world_building:
                messages = [
                    SystemMessage(content="你是一个十年以上的传奇游戏老玩家，精通传奇游戏世界设定。"),
                    HumanMessage(content=the_legend_plot_architecture_prompt.format(
                        core_seed=core_seed,
                        user_guidance='',
                        character_dynamics=character_dynamics,
                        world_building=world_building,
                    ))
                ]
            else:
                raise Exception('请先生成核心种子、角色动力学、世界构建矩阵')
        elif novel_param == 'chapter_blueprint':
            # 章节目录生成
            novel_architecture = self.get_novel_architecture(novel_id)
            messages = [
                SystemMessage(content="你是一个十年以上的传奇游戏老玩家，精通传奇游戏世界设定。"),
                HumanMessage(content=the_legend_chapter_blueprint_prompt.format(
                    user_guidance='',
                    novel_architecture=novel_architecture
                ))
            ]
        elif novel_param == 'chapter1':
            novel_architecture = self.get_novel_architecture(novel_id)
            chapter = novel_service.get_novel_blueprint_chapter(novel_id, 1)
            logger.bind(tag=TAG).info(f"chapter={chapter}")
            messages = [
                SystemMessage(content="你是一个十年以上的传奇游戏老玩家，精通传奇游戏世界设定。"),
                HumanMessage(content=the_legend_first_chapter_prompt.format(
                    position=chapter['position'],
                    rhythm=chapter['rhythm'],
                    attribute_changes=chapter['attribute_changes'],
                    character_development=chapter['character_development'],
                    foreshadow=chapter['foreshadow'],
                    novel_architecture=novel_architecture
                ))
            ]
        elif 'chapter' in novel_param and novel_param.startswith('chapter'):
            # 续写下一章，前面一章需要定稿
            chapter_num = int(novel_param.replace('chapter', ''))
            novel_architecture = self.get_novel_architecture(novel_id)
            chapter_current = novel_service.get_novel_blueprint_chapter(novel_id, chapter_num)
            chapter_next = novel_service.get_novel_blueprint_chapter(novel_id, chapter_num)
            previous_abstract = novel_service.novel_creation_params_query(novel_id, 'previous_abstract')
            # 获取上一章内容
            chapter_preview_content = novel_service.novel_creation_params_query(novel_id, f"chapter{chapter_num - 1}")
            previous_chapter_excerpt = ''
            for text in reversed(chapter_preview_content):
                if text.strip():
                    previous_chapter_excerpt = text[-800:] if len(text) > 800 else text
                    break
            character_state = novel_service.novel_creation_params_query(novel_id, 'character_state')
            # 章节内容提炼
            short_summary = ''
            messages = [
                SystemMessage(content="你是一个十年以上的传奇游戏老玩家，精通传奇游戏世界设定。"),
                HumanMessage(content=the_legend_next_chapter_prompt.format(
                    previous_abstract=previous_abstract,
                    previous_chapter_excerpt=previous_chapter_excerpt,
                    short_summary=short_summary,
                    character_state=character_state,
                    # 用户指导
                    user_guidance='',
                    # 本章节信息
                    current_chapter_number=chapter_num,
                    current_chapter_name=chapter_current['chapter_name'],
                    current_position=chapter_current['position'],
                    current_rhythm=chapter_current['rhythm'],
                    current_attribute_changes=chapter_current['attribute_changes'],
                    current_character_development=chapter_current['character_development'],
                    current_foreshadow=chapter_current['foreshadow'],
                    # 下一章目录信息
                    next_chapter_number=str(chapter_num + 1) if chapter_next else '',
                    next_chapter_name=chapter_next['chapter_name'] if chapter_next else '',
                    next_position=chapter_next['position'] if chapter_next else '',
                    next_rhythm=chapter_next['rhythm'] if chapter_next else '',
                    next_attribute_changes=chapter_next['attribute_changes'] if chapter_next else '',
                    next_character_development=chapter_next['character_development'] if chapter_next else '',
                    next_foreshadow=chapter_next['foreshadow'] if chapter_next else '',
                    novel_architecture=novel_architecture
                ))
            ]
        return llm_novel_stream(messages, novel_id, novel_param)

    def novel_creation_params_edit(self, novel_edit_params: NovelEditParams):
        """
        用户自行修改小说创作参数
        :param novel_edit_params:
        :param novel_id: 小说id
        :param novel_param: 小说参数
        :param content: 内容
        :return:
        """
        # 限制，不能修改前置步骤的内容
        novel_service.novel_creation_params_edit(novel_edit_params.novel_id, novel_edit_params.novel_param,
                                                 novel_edit_params.content)

    def get_novel_architecture(self, novel_id):
        """
        获取小说架构
        :param novel_id: 小说id
        :return:
        """
        core_seed = self.novel_creation_params_query(novel_id, 'core_seed')
        character_dynamics = self.novel_creation_params_query(novel_id, 'character_dynamics')
        world_building = self.novel_creation_params_query(novel_id, 'world_building')
        plot_architecture = self.novel_creation_params_query(novel_id, 'plot_architecture')

        return (
            "#=== 0) 小说设定 ===\n"
            f"类型：传奇游戏、，冒险,篇幅：5大阶段，每个阶段4章，一共20章，每章约800字\n\n"
            "#=== 1) 核心种子 ===\n"
            f"{core_seed}\n\n"
            "#=== 2) 角色动力学 ===\n"
            f"{character_dynamics}\n\n"
            "#=== 3) 世界观 ===\n"
            f"{world_building}\n\n"
            "#=== 4) 三幕式情节架构 ===\n"
            f"{plot_architecture}\n"
        )

    def get_chapter_blueprint(self, novel_id: str):
        """
        将目录转为结构化
        :param novel_id: 小说id
        :return:
        """
        return novel_service.get_novel_blueprint(novel_id)

    def novel_finalize_chapter(self, novel_id, chapter_no: str):
        # 更新前文摘要
        logger.bind(tag=TAG).info(f"更新前文摘要，novel_id: {novel_id}, chapter_no: {chapter_no}")
        chapter_content = novel_service.novel_creation_params_query(novel_id, f"chapter{chapter_no}")
        previous_abstract = novel_service.novel_creation_params_query(novel_id, "previous_abstract")
        new_previous_abstract = llm_chain(
            sys_prompt=None,
            user_prompt=the_legend_summary_update_prompt.format(
                current_chapter=chapter_no,
                completed_chapters=chapter_content,
                previous_abstract=previous_abstract
            ),
        )
        novel_service.novel_creation_params_edit(novel_id, 'previous_abstract', new_previous_abstract)
        logger.bind(tag=TAG).info(f"更新角色状态，novel_id: {novel_id}, chapter_no: {chapter_no}")
        # 更新更新角色状态
        current_character_status = novel_service.novel_creation_params_query(novel_id, 'character_status')
        character_dynamics = novel_service.novel_creation_params_query(novel_id, 'character_dynamics')
        new_character_status = llm_chain(
            sys_prompt=None,
            user_prompt=the_legend_character_update_prompt.format(
                current_chapter=chapter_no,
                chapter_content=chapter_content,
                character_dynamics=character_dynamics,
                current_character_status=current_character_status
            )
        )
        novel_service.novel_creation_params_edit(novel_id, 'character_status', new_character_status)
        logger.bind(tag=TAG).info(f"章节定稿完成，novel_id: {novel_id}, chapter_no: {chapter_no}")
