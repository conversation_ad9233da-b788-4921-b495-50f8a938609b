from typing import List

from pydantic import Field, BaseModel


class CodeTplFiledSchema(BaseModel):
    java_field_name: str = Field(None, description="Java字段名")
    java_field_name_f_u: str = Field(None, description="Java字段名（首字母大写）")
    java_field_comment: str = Field(None, description="Java字段描述")
    java_type: str = Field(None, description="Java的数据类型")
    component_type: str = Field(None, description="Vue的组件类型")


class CodeTplVarResultSchema(BaseModel):
    table_prefix: str = Field(None, description="表前缀")
    table_name: str = Field(None, description="表名称")
    table_comment: str = Field(None, description="表前缀")
    class_name: str = Field(None, description="Java类名")
    obj_name: str = Field(None, description="Java对象名称")
    api_path: str = Field(None, description="接口路径")
    router_path: str = Field(None, description="路由路径")
    has_date: bool = Field(False, description="是否含有date类型")
    has_decimal: bool = Field(False, description="是否含有decimal类型")
    has_date_time: bool = Field(False, description="是否含有date_time类型")
    field_list: List[CodeTplFiledSchema] = Field(None, description="Java类字段属性")
