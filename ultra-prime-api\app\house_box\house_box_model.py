from datetime import datetime
from typing import Optional

from pydantic import BaseModel, field_serializer
from sqlmodel import SQLModel, Field

from app.util.snowflake_utils import snow


# 请求模型
class CategoryCreateRequest(BaseModel):
    category_name: str
    remark: Optional[str] = None


class CategoryUpdateRequest(BaseModel):
    category_name: Optional[str] = None
    remark: Optional[str] = None


class SupplyCreateRequest(BaseModel):
    name: str
    category_id: Optional[int] = None
    unit: Optional[str] = None
    min_quantity: float = 0.0
    location: Optional[str] = None
    image_url: Optional[str] = None
    remark: Optional[str] = None


class SupplyUpdateRequest(BaseModel):
    name: Optional[str] = None
    category_id: Optional[int] = None
    unit: Optional[str] = None
    min_quantity: Optional[float] = None
    location: Optional[str] = None
    image_url: Optional[str] = None
    remark: Optional[str] = None


class PrimeHouseBoxCategorySchema(SQLModel, table=True):
    __tablename__ = "prime_house_box_categories"

    id: int = Field(default_factory=lambda: str(snow.next_id()), primary_key=True, title="ID主键")
    category_name: str = Field(max_length=50)
    remark: Optional[str] = Field(default=None, max_length=500)
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)

    @field_serializer('id')
    def serialize_id(self, v):
        return str(v)


class PrimeHouseBoxSupplySchema(SQLModel, table=True):
    __tablename__ = "prime_house_box_supplies"

    id: int = Field(default_factory=lambda: str(snow.next_id()), primary_key=True, title="ID主键")
    name: str = Field(max_length=100)
    category_id: Optional[int] = Field(default=None)
    unit: Optional[str] = Field(default=None, max_length=20)
    min_quantity: float = Field(default=1.0)
    quantity: float = Field(default=0.0)
    location: Optional[str] = Field(default=None, max_length=100)
    image_url: Optional[str] = Field(default=None, max_length=255)
    remark: Optional[str] = Field(default=None, max_length=500)
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)

    @field_serializer('id')
    def serialize_id(self, v):
        return str(v)

    @field_serializer('category_id')
    def serialize_category_id(self, v):
        return str(v)
