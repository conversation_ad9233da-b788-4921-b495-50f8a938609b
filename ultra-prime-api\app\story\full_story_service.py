import asyncio
import os
from concurrent.futures import Thread<PERSON>oolExecutor
from datetime import datetime
from typing import List, Optional

from sqlalchemy import text
from sqlmodel import select

from app.clip.full_story_poetry_clip_service import FullStoryPoetryClipService
from app.image.image_model import ImageGenerationRequest
from app.image.image_service import ImageOpenAIService
from app.story.story_model import (
    FullStoryDramaSchema,
    FullStoryCharacterSchema,
    FullStorySchema,
    FullStoryDialogueSchema, CreateStoryDramaRequest, FullStoryCharacterRequest, FullStorySceneImageRequest,
    FullStorySceneVideoRequest
)
from app.story.story_service import StoryService
from app.util import const, common_utils
from app.util.snowflake_utils import snow
from app.util.state_manager import RedisStateManager
from app.video.video_model import VideoGenerationRequest
from app.video.video_service import DashscopeVideoService
from app.voice.voice_service import VoiceEdgeTtsService
from app.writer.writer_model import FullStoryDrama
from app.writer.writer_service import WriterService
from config import setup_logging

TAG = __name__
logger = setup_logging()

# 创建线程池用于异步任务
executor = ThreadPoolExecutor(max_workers=4)


class FullStoryService(StoryService):

    def save_full_story_scene_image(self, request: FullStorySceneImageRequest):
        session = self._get_session()
        try:
            scene = session.get(FullStoryDramaSchema, request.id)
            scene.scene_drawing_prompt = request.scene_drawing_prompt
            scene.scene_drawing_negative_prompt = request.scene_drawing_negative_prompt
            scene.update_time = datetime.utcnow()
            session.add(scene)
            session.commit()
        finally:
            session.close()

    def generate_full_story_scene_image(self, request: FullStorySceneImageRequest):
        session = self._get_session()
        try:
            full_story = self.get_full_story_detail(request.story_id)
            image_service = ImageOpenAIService()
            image_request = ImageGenerationRequest(
                prompt=request.scene_drawing_prompt,
                image_size=full_story.story_scale,
                image_style=full_story.story_style
            )
            file_url = image_service.generate_image_async(image_request)
            # 保存到本地目录
            common_utils.task_dir(full_story.id)
            # 保存到本地路径
            common_utils.download_web_image(file_url,
                                            os.path.join(common_utils.task_dir(str(full_story.id) + '/scenes'),
                                                         f"{request.id}.png"))
            scene = session.get(FullStoryDramaSchema, request.id)
            scene.drama_scene_cover_url = file_url
            session.add(scene)
            session.commit()
        finally:
            session.close()

    def save_full_story_scene_video(self, request: FullStorySceneVideoRequest):
        session = self._get_session()
        try:
            scene = session.get(FullStoryDramaSchema, request.id)
            scene.video_generation_prompt = request.video_generation_prompt
            scene.video_generation_negative_prompt = request.video_generation_negative_prompt
            scene.update_time = datetime.utcnow()
            session.add(scene)
            session.commit()
        finally:
            session.close()

    def _generate_full_story_scene_video_task(self, video_generation_request: VideoGenerationRequest, story_id: str,
                                              scene_id: str, scene_video_file_path: str, state_manager):
        session = self._get_session()
        try:
            video_service = DashscopeVideoService()
            file_url = video_service.submit_video_task(video_generation_request, state_manager)
            state_manager.update_task(progress=60)
            # 保存到本地目录
            common_utils.task_dir(str(story_id))
            # 保存到本地路径
            common_utils.download_web_image(file_url, scene_video_file_path)
            state_manager.update_task(progress=80)

            scene = session.get(FullStoryDramaSchema, scene_id)
            scene.drama_video_source_url = file_url
            session.add(scene)
            session.commit()
            state_manager.update_task(progress=100, state=const.TASK_STATE_COMPLETE)
        finally:
            session.close()

    def generate_full_story_scene_video(self, request: FullStorySceneVideoRequest):
        session = self._get_session()
        state_manager = RedisStateManager(request.story_id)
        try:
            state_manager.update_task(progress=10)
            full_story = self.get_full_story_detail(request.story_id)
            scene_video_file_path = os.path.join(common_utils.task_dir(str(full_story.id) + '/scenes'),
                                                 f"{request.id}.mp4")

            video_generation_request = VideoGenerationRequest(
                task_id=str(full_story.id),
                prompt=request.video_generation_prompt,
                negative_prompt=request.video_generation_negative_prompt,
                image_path=scene_video_file_path.replace('.mp4', '.png'),
            )
            executor.submit(self._generate_full_story_scene_video_task, video_generation_request, request.story_id,
                            request.id, scene_video_file_path, state_manager)
        except Exception as e:
            logger.bind(tag=TAG).error(f"获取故事失败: {str(e)}")
            state_manager.update_task(state=const.TASK_STATE_FAILED)
            raise
        finally:
            session.close()

    def save_full_story_character(self, request: FullStoryCharacterRequest):
        session = self._get_session()
        try:
            character = session.get(FullStoryCharacterSchema, request.id)
            character.character_dub = request.character_dub
            character.generate_character_prompt = request.generate_character_prompt
            character.generate_character_negative_prompt = request.generate_character_negative_prompt
            character.update_time = datetime.utcnow()
            session.add(character)
            session.commit()
        finally:
            session.close()

    def generate_full_story_character_image(self, request: FullStoryCharacterRequest):
        self.save_full_story_character(request)
        # 根据提示词生成图
        session = self._get_session()
        full_story = self.get_full_story_detail(request.story_id)
        image_service = ImageOpenAIService()
        image_request = ImageGenerationRequest(
            prompt=request.generate_character_prompt,
            image_size='9:16',
            image_style=full_story.story_style
        )
        try:
            file_url = image_service.generate_image_async(image_request)
            character = session.get(FullStoryCharacterSchema, request.id)
            character.character_image_url = file_url
            session.add(character)
            session.commit()
        finally:
            session.close()

    def _process_full_story_drama_async(self, story_id: str, story_type: str, story_content: str, state_manager):
        """
        异步处理故事剧本生成
        """
        try:
            logger.bind(tag=TAG).info(f"开始处理故事剧本，story_id: {story_id}，story_type: {story_type}")

            # 1. 调用LLM生成剧本
            writer_service = WriterService()

            full_story_drama = writer_service.generate_full_story_drama(story_type, story_content)
            state_manager.update_task(progress=40)
            # 2. 存储到数据库
            self._save_full_story_drama_to_db(story_id, full_story_drama, state_manager)

            # 3. 更新状态为完成
            state_manager.update_task(progress=100, state=const.TASK_STATE_COMPLETE)

        except Exception as e:
            logger.bind(tag=TAG).error(f"处理故事剧本失败，story_id: {story_id}, 错误: {str(e)}")
            state_manager.update_task(state=const.TASK_STATE_FAILED)
            # 失败时删除已新增的 NovelStory 记录
            try:
                session = self._get_session()
                try:
                    story = session.get(FullStorySchema, story_id)
                    if story:
                        session.delete(story)
                        session.commit()
                finally:
                    session.close()
            except Exception as db_error:
                logger.bind(tag=TAG).error(f"删除失败小说记录失败: {str(db_error)}")

    def _save_full_story_drama_to_db(self, story_id: str, llm_response: FullStoryDrama, state_manager):
        """
        将LLM响应保存到数据库
        """
        session = self._get_session()
        try:
            # 保存角色信息
            character_map = {}
            if hasattr(llm_response, 'characters') and llm_response.characters:
                for char in llm_response.characters:
                    character = FullStoryCharacterSchema(
                        story_id=story_id,
                        character_dub='zh-CN-XiaoxiaoNeural',
                        character_name=char.character_name if hasattr(char, 'character_name') else "",
                        character_description=char.character_description if hasattr(char,
                                                                                    'character_description') else "",
                        generate_character_prompt=char.generate_character_prompt if hasattr(char,
                                                                                            'generate_character_prompt') else "",
                    )
                    session.add(character)
                    character_map[char.character_name if hasattr(char, 'character_name') else ""] = character.id
            state_manager.update_task(progress=45)
            # 保存镜头信息
            if hasattr(llm_response, 'scenes') and llm_response.scenes:
                for scene in llm_response.scenes:
                    drama = FullStoryDramaSchema(
                        story_id=story_id,
                        drama_no=scene.drama_no if hasattr(scene, 'drama_no') else "",
                        camera_angle=scene.camera_angle if hasattr(scene, 'camera_angle') else "",
                        shot_type=scene.shot_type if hasattr(scene, 'shot_type') else "",
                        camera_movement=scene.camera_movement if hasattr(scene, 'camera_movement') else "",
                        characters=','.join(scene.characters) if hasattr(scene,
                                                                         'characters') and scene.characters else "",
                        scene_description=scene.scene_description if hasattr(scene, 'scene_description') else "",
                        scene_drawing_prompt=scene.scene_drawing_prompt if hasattr(scene,
                                                                                   'scene_drawing_prompt') else "",
                        video_generation_prompt=scene.video_generation_prompt if hasattr(scene,
                                                                                         'video_generation_prompt') else "",
                        dialogues=','.join(scene.dialogues) if hasattr(scene,
                                                                       'dialogues') and scene.dialogues else "",
                        sound_effect=scene.sound_effect if hasattr(scene, 'sound_effect') else "",

                    )
                    session.add(drama)
            state_manager.update_task(progress=60)
            # 保存对话信息
            if hasattr(llm_response, 'dialogues') and llm_response.dialogues:
                for dialogue in llm_response.dialogues:
                    dialogue_record = FullStoryDialogueSchema(
                        story_id=story_id,
                        drama_no=dialogue.drama_no if hasattr(dialogue, 'drama_no') else "",
                        role=dialogue.role if hasattr(dialogue, 'role') else "",
                        line=dialogue.line if hasattr(dialogue, 'line') else "",
                        emotion=dialogue.emotion if hasattr(dialogue, 'emotion') else "",
                    )
                    session.add(dialogue_record)
            session.commit()
            logger.bind(tag=TAG).info(f"故事剧本数据保存成功，story_id: {story_id}")
            state_manager.update_task(progress=75)
        except Exception as e:
            session.rollback()
            logger.bind(tag=TAG).error(f"保存故事剧本数据失败: {str(e)}")
            raise
        finally:
            session.close()

    def get_full_story_detail(self, story_id: str):
        try:
            session = self._get_session()
            try:
                return session.get(FullStorySchema, story_id)
            finally:
                session.close()
        except Exception as e:
            logger.bind(tag=TAG).error(f"获取故事失败: {str(e)}")
            raise

    def create_full_story_drama(self, request: CreateStoryDramaRequest, user_id: str) -> str:
        """
        创建故事剧本，异步处理
        :param user_id:
        :param request: CreateStoryDramaRequest
        :return: story_id (数据库ID)
        """
        # 生成story_id
        story_id = str(snow.next_id())
        state_manager = RedisStateManager(story_id)
        # 先创建基础记录
        session = self._get_session()
        try:
            story = FullStorySchema(
                id=story_id,
                user_id=user_id,
                story_name=request.story_name,
                story_type=request.story_type,
                story_content=request.story_content,
                story_scale=request.story_scale,
                story_style=request.story_style,
                remark="processing"  # 使用remark字段存储状态
            )
            session.add(story)
            session.commit()
        except Exception as e:
            session.rollback()
            logger.bind(tag=TAG).error(f"创建基础记录失败: {str(e)}")
            raise
        finally:
            session.close()
        state_manager.update_task(progress=10)
        # 提交异步任务
        executor.submit(self._process_full_story_drama_async, story_id, request.story_type, request.story_content,
                        state_manager)

        logger.bind(tag=TAG).info(f"故事剧本创建任务已提交，story_id: {story_id}")
        return story_id

    def get_full_story_drama(self, story_id: str) -> List[FullStoryDramaSchema]:
        """
        获取故事剧本镜头
        :param story_id: 故事ID
        :return: 剧本镜头列表
        """
        session = self._get_session()
        try:
            # 查询剧本镜头，按创建时间排序
            statement = select(FullStoryDramaSchema).where(
                FullStoryDramaSchema.story_id == story_id
            ).order_by(FullStoryDramaSchema.create_time)

            dramas = session.exec(statement).all()

            return list(dramas)
        finally:
            session.close()

    def get_full_story_status(self, story_id: str) -> Optional[str]:
        """
        获取故事处理状态
        :param story_id: 故事ID
        :return: 状态字符串 (processing/completed/failed)
        """
        state_manager = RedisStateManager(story_id)
        task = state_manager.get_task()
        if task:
            return {
                'state': str(task['state']),
                'progress': int(task['progress'])
            }
        else:
            return {
                'state': '1',
                'progress': 0
            }

    def get_full_story_characters(self, story_id: str) -> List[FullStoryCharacterSchema]:
        """
        获取故事角色列表
        :param story_id: 故事ID
        :return: 角色列表
        """
        session = self._get_session()
        try:
            # 查询角色列表
            statement = select(FullStoryCharacterSchema).where(
                FullStoryCharacterSchema.story_id == story_id
            )
            return session.exec(statement).all()
        finally:
            session.close()

    def _generate_full_story_voice_subtitle_task(self, subtitles, state_manager):
        vets = VoiceEdgeTtsService()
        for subtitle in subtitles:
            audio_file_path = os.path.join(common_utils.task_dir(subtitle[1]), f'scenes/{subtitle[0]}.mp3')
            subtitle_file_path = os.path.join(common_utils.task_dir(subtitle[1]), f'scenes/{subtitle[0]}.srt')
            asyncio.run(vets.speech_synthesis_local(subtitle[3], audio_file_path, subtitle_file_path,
                                                    subtitle[2]))
        state_manager.update_task(progress=30)
        # 合并视频
        full_story_poetry_clip_service = FullStoryPoetryClipService()


    def generate_full_story_voice_subtitle(self, story_id: str):
        session = self._get_session()
        state_manager = RedisStateManager(story_id)
        state_manager.update_task(progress=10)
        try:
            sql_query = """
                        SELECT a.id,a.story_id, c.character_dub, b.line, b.emotion \
                        FROM aigc_full_story_drama a \
                                 INNER JOIN aigc_full_story_dialogue b \
                                            ON a.story_id = b.story_id AND a.drama_no = b.drama_no \
                                 INNER JOIN aigc_full_story_character c \
                                            ON a.story_id = c.story_id AND b.role = c.character_name
                        WHERE a.story_id = '{story_id}'
                        ORDER BY a.drama_no ASC \
                        """.format(story_id=story_id)
            subtitles = session.exec(text(sql_query)).fetchall()
            executor.submit(self._generate_full_story_voice_subtitle_task, subtitles, state_manager)
        finally:
            session.close()

    def generate_full_story_final_video(self, story_id: str):
        pass
