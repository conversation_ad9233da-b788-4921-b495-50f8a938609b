from app.clip.full_story_poetry_clip_service import FullStoryPoetryClipService
from app.video.video_model import VideoAspect

if __name__ == '__main__':
    full_story_poetry_clip = FullStoryPoetryClipService()
    full_story_poetry_clip.composite_video(
        video_file_path=r'D:\Projects\SPACE\ultra-prime-project\ultra-prime-api\data\607752118228619264\scenes\607752280330080256.mp4',
        voice_file_path=r'D:\Projects\SPACE\ultra-prime-project\ultra-prime-api\data\607752118228619264\scenes\607752280330080256.mp3',
        subtitle_file_path=r'D:\Projects\SPACE\ultra-prime-project\ultra-prime-api\data\607752118228619264\scenes\607752280330080256.srt',
        output_file_path=r'D:\Projects\SPACE\ultra-prime-project\ultra-prime-api\data\607752118228619264\scenes\607752280330080256_composite.mp4',
        video_aspect=VideoAspect.landscape
    )
    print('完成！')
