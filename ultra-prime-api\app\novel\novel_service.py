import re
from datetime import datetime, UTC

from pymongo import MongoClient

from app.novel.novel_model import NovelChapterBlueprint, NovelBasicInfo
from config import Config, setup_logging

TAG = __name__
logger = setup_logging()


class NovelService:
    def __init__(self):
        client = MongoClient(Config.MONGO_URI)
        self.db = client[Config.APP_MONGO_DB]
        self.novel_params_collection = self.db["novel_params"]
        self.novel_blueprint_collection = self.db["novel_blueprint"]
        self.novel_chapter_collection = self.db["novel_chapter"]

    def _has_chapter_key(self, novel_param):
        return re.fullmatch(r'chapter\d+', novel_param, flags=re.I)

    def get_novel_basic_info(self, novel_id: str, user_id: str):
        """
        查询小说基本信息
        """
        doc = self.novel_params_collection.find_one({"novel_id": novel_id, "user_id": user_id})
        if doc:
            return {
                "novel_id": doc.get("novel_id", ""),
                "novel_cover": doc.get("novel_cover", ""),
                "novel_name": doc.get("novel_name", ""),
                "chapter_count": doc.get("chapter_count", 10),
                "words_per_chapter": doc.get("words_per_chapter", 800),
                "novel_author": doc.get("novel_author", ""),
                "novel_desc": doc.get("novel_desc", "")
            }
        return None

    def get_user_novel_work(self, user_id: str):
        """
           获取用户的小说作品列表
           :param user_id: 用户ID
           :return: 小说作品列表
           """
        try:
            # 查询用户的所有小说作品
            cursor = self.novel_params_collection.find(
                {"user_id": user_id},
                {
                    "_id": 0,
                    "created_at": 1,
                    "novel_id": 1,
                    "novel_cover": 1,
                    "novel_name": 1,
                    "novel_author": 1,
                    "novel_desc": 1
                }
            ).sort("updated_at", -1)  # 按更新时间倒序排列

            novels = list(cursor)
            logger.bind(tag=TAG).info(f"获取用户小说作品成功，用户ID: {user_id}, 数量: {len(novels)}")
            print(novels)
            return novels

        except Exception as e:
            logger.bind(tag=TAG).error(f"获取用户小说作品失败，用户ID: {user_id}, 错误: {e}")
            return []

    def remove_user_novel_work(self, novel_id: str, user_id: str):
        """
            删除用户的小说作品
            :param novel_id: 小说ID
            :param user_id: 用户ID
            :return: 删除结果
            """
        try:
            # 确保只能删除自己创建的小说
            result = self.novel_params_collection.delete_one(
                {
                    "novel_id": novel_id,
                    "user_id": user_id
                }
            )

            if result.deleted_count > 0:
                logger.bind(tag=TAG).info(f"删除用户小说作品成功，小说ID: {novel_id}, 用户ID: {user_id}")
                return True
            else:
                logger.bind(tag=TAG).warning(
                    f"删除用户小说作品失败，未找到匹配记录，小说ID: {novel_id}, 用户ID: {user_id}")
                return False

        except Exception as e:
            logger.bind(tag=TAG).error(f"删除用户小说作品异常，小说ID: {novel_id}, 用户ID: {user_id}, 错误: {e}")
            return False

    def save_novel_basic_info(self, novel_basic_info: NovelBasicInfo):
        update_data = novel_basic_info.model_dump(exclude_none=True)
        logger.bind(tag=TAG).info(f"update_data={update_data}")
        self.novel_params_collection.update_one(
            {"novel_id": novel_basic_info.novel_id},
            {
                "$set": update_data,
                "$currentDate": {"updated_at": True}
            },
            upsert=True
        )

    def novel_creation_params_query(self, novel_id, novel_param):
        """
        查询小说创作参数内容
        :param novel_id: 小说id
        :param novel_param: 小说参数
        :return:
        """
        """
        查询小说创作参数内容
        """
        if self._has_chapter_key(novel_param):
            doc = self.novel_chapter_collection.find_one({"novel_id": novel_id})
        else:
            doc = self.novel_params_collection.find_one({"novel_id": novel_id})
        if doc:
            return doc.get(novel_param, None)
        return None

    def novel_creation_params_edit(self, novel_id, novel_param, content):
        """
        保存或更新小说创作参数。novel_param 作为字段名，content 作为字段值。
        """
        self.novel_params_collection.update_one(
            {"novel_id": novel_id},
            {"$set": {novel_param: content, "updated_at": datetime.now(UTC)}},
            upsert=True
        )
        logger.bind(tag=TAG).info(f"Saved novel creation params for novel_id: {novel_id}, param: {novel_param}")

    def save_novel_blueprint(self, novel_id, content: NovelChapterBlueprint):
        """
        保存或更新小说目录。content 是 NovelChapterBlueprint，只存 blueprint 字段（转为 dict）。
        """
        # 将所有 NovelChapterVol/NoveChapter 转为 dict
        blueprint_list = []
        for vol in content.blueprint:
            # Pydantic v1: vol.dict(), v2: vol.model_dump()
            vol_dict = vol.dict() if hasattr(vol, 'dict') else vol.model_dump()
            # 递归转换 chapters
            vol_dict['chapters'] = [
                ch.dict() if hasattr(ch, 'dict') else ch.model_dump()
                for ch in vol.chapters
            ]
            blueprint_list.append(vol_dict)

        self.novel_blueprint_collection.update_one(
            {"novel_id": novel_id},
            {"$set": {"blueprint": blueprint_list, "updated_at": datetime.now(UTC)}},
            upsert=True
        )

    def get_novel_blueprint_chapter(self, novel_id, chapter_num: int):
        pipeline = [
            {"$match": {"novel_id": novel_id}},
            {"$unwind": "$blueprint"},
            {"$unwind": "$blueprint.chapters"},
            {"$match": {"blueprint.chapters.chapter_no": f"第{chapter_num}章"}},
            {"$project": {
                "chapter_no": "$blueprint.chapters.chapter_no",
                "chapter_name": "$blueprint.chapters.chapter_name",
                "position": "$blueprint.chapters.position",
                "rhythm": "$blueprint.chapters.rhythm",
                "attribute_changes": "$blueprint.chapters.attribute_changes",
                "character_development": "$blueprint.chapters.character_development",
                "foreshadow": "$blueprint.chapters.foreshadow",
                "_id": 0
            }}
        ]
        result = list(self.novel_blueprint_collection.aggregate(pipeline))
        return result[0] if result else None

    def get_novel_blueprint(self, novel_id):
        """
        查询小说目录
        """
        doc = self.novel_blueprint_collection.find_one({"novel_id": novel_id})
        if doc:
            return doc.get("blueprint", [])
        return []

    def save_novel_chapter(self, novel_id, chapter_number, content):
        """
        保存或更新小说章节内容。chapter_number 作为字段名，content 作为字段值。
        """
        self.novel_chapter_collection.update_one(
            {"novel_id": novel_id},
            {"$set": {chapter_number: content, "updated_at": datetime.now(UTC)}},
            upsert=True
        )
        logger.bind(tag=TAG).info(f"Saved novel chapter for novel_id: {novel_id}, chapter: {chapter_number}")
