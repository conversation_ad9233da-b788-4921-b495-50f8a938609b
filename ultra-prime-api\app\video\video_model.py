from enum import Enum
from typing import Optional

from pydantic import BaseModel

from app.util.state_manager import RedisStateManager


class MaterialInfo:
    provider: str = "pexels"
    url: str = ""
    duration: int = 0


class VideoAspect(str, Enum):
    landscape = "16:9"
    portrait = "9:16"

    def to_resolution(self):
        if self == VideoAspect.landscape.value:
            return 1920, 1080
        elif self == VideoAspect.portrait.value:
            return 1080, 1920
        return 1080, 1920


class VideoConcatMode(str, Enum):
    random = "random"
    sequential = "sequential"


class VideoTransitionMode(str, Enum):
    none = None
    shuffle = "Shuffle"
    fade_in = "FadeIn"
    fade_out = "FadeOut"
    slide_in = "SlideIn"
    slide_out = "SlideOut"


class VideoGenerationRequest(BaseModel):
    task_id: str
    prompt: str
    image_path: str
    negative_prompt: Optional[str] = None
