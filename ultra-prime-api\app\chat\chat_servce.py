from datetime import UTC, datetime
from typing import List

from pymongo import MongoClient
from sqlmodel import Session, select

from app.chat.chat_model import Chat<PERSON><PERSON><PERSON>, ChatAgentClassifySchema, ChatAgentModelSchema
from config import Config, setup_logging
from dependencies import get_session

TAG = __name__
logger = setup_logging()


class ChatService:
    def __init__(self):
        client = MongoClient(Config.MONGO_URI)
        self.db = client[Config.APP_MONGO_DB]
        self.chat_collection = self.db["llm_chat_history"]

    def _get_session(self) -> Session:
        # 每次操作都获取新的session
        return next(get_session())

    def get_agent(self):
        session = self._get_session()
        try:
            statement = (
                select(ChatAgentModelSchema, ChatAgentClassifySchema.classify_name)
                .join(ChatAgentClassifySchema, ChatAgentModelSchema.classify_id == ChatAgentClassifySchema.id)
            )
            results = session.exec(statement).all()
            return [
                {
                    "agent_id": str(agent.id),
                    "agent_name": agent.agent_name,
                    "classify_name": classify_name,
                    "agent_description": agent.agent_description,
                    "agent_icon": agent.agent_icon,
                    "agent_prompt": agent.agent_prompt,
                    "agent_initial_value": agent.agent_initial_value,
                }
                for agent, classify_name in results
            ]
        finally:
            session.close()

    def get_history(self, user_id, agent_id):
        """
        查询某个user_id下某个agent_id的历史消息，返回消息列表
        """
        doc = self.chat_collection.find_one({"user_id": user_id, "agent_id": agent_id})
        if doc and "messages" in doc:
            return doc["messages"]
        return []

    def init_history(self, user_id, agent_id, messages: List[ChatMessage]):
        """
        初始化历史消息，如果不存在则创建，存在则跳过
        """
        # 检查是否已经存在历史记录
        existing_doc = self.chat_collection.find_one({"user_id": user_id, "agent_id": agent_id})

        # 如果已经存在，则跳过
        if existing_doc:
            logger.bind(tag=TAG).info(
                f"History already exists for user_id: {user_id}, agent_id: {agent_id}, skipping initialization")
            return

        # 如果不存在，则创建新的历史记录
        now = datetime.now(UTC).timestamp()
        message_list = [{"role": msg.role, "content": msg.content} for msg in messages]

        self.chat_collection.insert_one({
            "user_id": user_id,
            "agent_id": agent_id,
            "messages": message_list,
            "timestamp": now
        })
        logger.bind(tag=TAG).info(
            f"Initialized history for user_id: {user_id}, agent_id: {agent_id} with {len(messages)} messages")

    def save_history(self, user_id, agent_id, role, content):
        """
        仅追加消息到已存在的历史记录，如果记录不存在则跳过
        """
        # 检查记录是否存在
        existing_doc = self.chat_collection.find_one({"user_id": user_id, "agent_id": agent_id})

        if not existing_doc:
            logger.bind(tag=TAG).warning(
                f"History not found for user_id: {user_id}, agent_id: {agent_id}, skipping save")
            return

        # 如果存在，则追加消息
        now = datetime.now(UTC).timestamp()
        result = self.chat_collection.update_one(
            {"user_id": user_id, "agent_id": agent_id},
            {"$push": {"messages": {"role": role, "content": content}}, "$set": {"timestamp": now}}
        )

        if result.modified_count > 0:
            logger.bind(tag=TAG).info(f"Saved message for user_id: {user_id}, agent_id: {agent_id}, role: {role}")
        else:
            logger.bind(tag=TAG).warning(f"Failed to save message for user_id: {user_id}, agent_id: {agent_id}")

    def clear_history(self, user_id, agent_id):
        """
        清空某个user_id下某个agent_id的所有历史消息
        """
        self.chat_collection.delete_one({"user_id": user_id, "agent_id": agent_id})
