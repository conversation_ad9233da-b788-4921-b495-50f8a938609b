from fastapi import APIRouter, Request

from app.common.common_model import OnlyOfficeCallbackRequest
from app.common.common_service import CommonService
from config import setup_logging

TAG = __name__
logger = setup_logging()

router = APIRouter(prefix="/common", tags=["Common接口"])

common_service = CommonService()


@router.post("/only_office_callback")
async def only_office_callback(request: Request):
    body = await request.json()
    logger.bind(tag=TAG).info(body)
    # 转换为实体对象
    callback_request = OnlyOfficeCallbackRequest(**body)
    return common_service.only_office_callback(callback_request)
