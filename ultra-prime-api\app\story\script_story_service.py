import asyncio
import os.path
from concurrent.futures import Thread<PERSON>oolExecutor

from sqlmodel import select

from app.clip.script_story_clip_service import ScriptStoryClipService
from app.story.story_model import ScriptStoryParams, ScriptStorySchema
from app.story.story_service import StoryService
from app.util import const, common_utils
from app.util.bucket_utils import MinioBucketUtils
from app.util.snowflake_utils import snow
from app.util.state_manager import RedisStateManager
from app.video.video_model import VideoAspect, VideoConcatMode, VideoTransitionMode
from app.video.video_search import get_video_materials
from app.voice.voice_service import VoiceEdgeTtsService
from app.writer.writer_service import WriterService
from config import setup_logging

TAG = __name__
logger = setup_logging()

# 创建线程池用于异步任务
executor = ThreadPoolExecutor(max_workers=4)


class ScriptStoryService(StoryService):
    def __init__(self):
        pass

    def _save_script_story_data(self, request: ScriptStoryParams, user_id):
        session = self._get_session()
        try:
            story = ScriptStorySchema(
                user_id=user_id,
                story_subject=request.story_subject)
            session.add(story)
            session.commit()
            return story.id
        except Exception as e:
            session.rollback()
            logger.bind(tag=TAG).error(f"保存故事剧本数据失败: {str(e)}")
            raise
        finally:
            session.close()

    def _save_script_story_video_paths(self, story_id: str, video_paths: str):
        session = self._get_session()
        try:
            statement = select(ScriptStorySchema).where(
                ScriptStorySchema.id == story_id
            )
            story = session.exec(statement).one()
            story.video_paths = video_paths
            session.add(story)
            session.commit()
            logger.bind(tag=TAG).info("保存视频素材完成")
        except Exception as e:
            session.rollback()
            logger.bind(tag=TAG).error(f"保存视频素材失败: {str(e)}")
            raise
        finally:
            session.close()

    def _save_script_story_voice_file(self, story_id: str, voice_file: str, subtitle_file: str, voice_duration: float):
        session = self._get_session()
        try:
            statement = select(ScriptStorySchema).where(
                ScriptStorySchema.id == story_id
            )
            story = session.exec(statement).one()
            story.voice_file = voice_file
            story.voice_duration = voice_duration
            story.subtitle_file = subtitle_file
            session.add(story)
            session.commit()
            logger.bind(tag=TAG).info("保存配音文件完成")
        except Exception as e:
            session.rollback()
            logger.bind(tag=TAG).error(f"保存配音文件失败: {str(e)}")
            raise
        finally:
            session.close()

    def _save_script_story_video_script(self, story_id: str, video_script: str):
        session = self._get_session()
        try:
            statement = select(ScriptStorySchema).where(
                ScriptStorySchema.id == story_id
            )
            story = session.exec(statement).one()
            story.video_script = video_script
            session.add(story)
            session.commit()
            logger.bind(tag=TAG).info("保存视频脚本完成")
        except Exception as e:
            session.rollback()
            logger.bind(tag=TAG).error(f"保存视频脚本失败: {str(e)}")
            raise
        finally:
            session.close()

    def _save_script_story_video_terms(self, story_id: str, video_terms: str):
        session = self._get_session()
        try:
            statement = select(ScriptStorySchema).where(
                ScriptStorySchema.id == story_id
            )
            story = session.exec(statement).one()
            story.video_terms = video_terms
            session.add(story)
            session.commit()
            logger.bind(tag=TAG).info("保存视频搜索词完成")
        except Exception as e:
            session.rollback()
            logger.bind(tag=TAG).error(f"保存视频搜索词失败: {str(e)}")
            raise
        finally:
            session.close()

    def _save_script_story_final_video_url(self, story_id: str, final_video_url: str, bgm_file: str):
        session = self._get_session()
        try:
            statement = select(ScriptStorySchema).where(
                ScriptStorySchema.id == story_id
            )
            story = session.exec(statement).one()
            story.final_video_url = final_video_url
            story.bgm_file = bgm_file
            session.add(story)
            session.commit()
            logger.bind(tag=TAG).info("保存最终视频完成")
        except Exception as e:
            session.rollback()
            logger.bind(tag=TAG).error(f"保存最终视频失败: {str(e)}")
            raise
        finally:
            session.close()

    def _save_script_story_combined_video_path(self, story_id: str, combined_video_path: str):
        session = self._get_session()
        try:
            statement = select(ScriptStorySchema).where(
                ScriptStorySchema.id == story_id
            )
            story = session.exec(statement).one()
            story.combined_video_path = combined_video_path
            session.add(story)
            session.commit()
            logger.bind(tag=TAG).info("保存合并视频完成")
        except Exception as e:
            session.rollback()
            logger.bind(tag=TAG).error(f"保存合并视频失败: {str(e)}")
            raise
        finally:
            session.close()

    def _delete_script_story_data(self, story_id: str):
        session = self._get_session()
        try:
            statement = select(ScriptStorySchema).where(
                ScriptStorySchema.id == story_id
            )
            story = session.exec(statement).one()
            session.delete(story)
            session.commit()
        except Exception as e:
            session.rollback()
            logger.bind(tag=TAG).error(f"删除故事剧本数据失败: {str(e)}")

    def _get_script_story_data(self, request: ScriptStoryParams):
        session = self._get_session()
        try:
            statement = select(ScriptStorySchema).where(
                ScriptStorySchema.id == request.story_id
            )
            story = session.exec(statement).one()
            if story:

                # 给request赋值
                request.task_id = story.task_id

                request.video_script = story.video_script
                request.video_terms = (
                    [term.strip() for term in story.video_terms.split(',')]
                    if story.video_terms and story.video_terms.strip()
                    else []
                )
                request.video_aspect = (
                    VideoAspect(story.video_aspect)
                    if story.video_aspect else None
                )
                request.video_concat_mode = (
                    VideoConcatMode(story.video_concat_mode)
                    if story.video_concat_mode else None
                )
                request.video_transition_mode = (
                    VideoTransitionMode(story.video_transition_mode)
                    if story.video_transition_mode else None
                )
                request.video_clip_duration = story.video_clip_duration

                request.video_source = story.video_source
                request.video_paths = (
                    [p.strip() for p in story.video_paths.split(',')]
                    if story.video_paths and story.video_paths.strip()
                    else []
                )
                request.final_video_url = story.final_video_url
                request.combined_video_path = story.combined_video_path
                # 配音
                request.voice_file = story.voice_file
                request.voice_name = story.voice_name
                request.voice_volume = story.voice_volume
                request.voice_rate = story.voice_rate
                request.voice_duration = story.voice_duration
                # 背景音乐
                request.bgm_file = story.bgm_file
                request.bgm_volume = story.bgm_volume
                # 字幕
                request.subtitle_file = story.subtitle_file
                request.subtitle_enabled = story.subtitle_enabled
                request.subtitle_position = story.subtitle_position
                request.custom_position = story.custom_position
                request.font_name = story.font_name
                request.text_fore_color = story.text_fore_color
                request.text_background_color = story.text_background_color
                request.font_size = story.font_size
                request.stroke_color = story.stroke_color
                request.stroke_width = story.stroke_width
                request.n_threads = story.n_threads
                request.paragraph_number = story.paragraph_number
            else:
                raise Exception("未找到脚本故事数据")
        finally:
            session.close()

    async def _generate_voice_srt_file(self, request: ScriptStoryParams, voice_file, subtitle_file):
        voice_service = VoiceEdgeTtsService()
        voice_duration = await voice_service.speech_synthesis_local(
            speech_text=request.video_script,
            audio_file_path=voice_file,
            subtitle_file_path=subtitle_file,
            voice=request.voice_name
        )
        request.voice_file = voice_file
        request.subtitle_file = subtitle_file
        request.voice_duration = voice_duration
        logger.bind(tag=TAG).info(f"生成音频完成，时长: {request.voice_duration}")

    def _process_story_drama_async(self, request: ScriptStoryParams, user_id: str, state_manager):
        """
        异步处理故事剧本生成
        """
        story_id = request.story_id
        writer_service = WriterService()
        try:
            if not story_id:
                # 保存参数
                story_id = self._save_script_story_data(request, user_id)
            else:
                # 支持断点续传
                self._get_script_story_data(request)
                state_manager.update_task(state=const.TASK_STATE_PROCESSING)
            # 1. 生成视频脚本
            if not request.video_script:
                logger.bind(tag=TAG).info("--->1. 生成视频脚本")
                video_script = writer_service.generate_script_story_video_script(request.story_subject)
                request.video_script = video_script
                self._save_script_story_video_script(story_id, request.video_script)
                state_manager.update_task(progress=10)
            # 2. 生成搜索关键词
            if not request.video_terms:
                logger.bind(tag=TAG).info("--->2. 生成搜索关键词")
                video_terms = writer_service.generate_script_story_video_terms(request.story_subject,
                                                                               request.video_script)
                request.video_terms = video_terms.search_terms
                self._save_script_story_video_terms(story_id, ','.join(request.video_terms))
                state_manager.update_task(progress=20)
            # 3. 生成音频 和 字幕文件
            if not request.voice_file:
                voice_file = os.path.join(common_utils.task_dir(story_id), 'audio.mp3')
                subtitle_file = os.path.join(common_utils.task_dir(story_id), 'subtitle.srt')
                logger.bind(tag=TAG).info("--->3. 生成音频和字幕文件")
                asyncio.run(self._generate_voice_srt_file(request, voice_file, subtitle_file))
                self._save_script_story_voice_file(story_id, request.voice_file, request.subtitle_file,
                                                   request.voice_duration)
                state_manager.update_task(progress=40)
                request.voice_file = voice_file
                request.subtitle_file = subtitle_file
            # 4. 根据关键词搜索视频素材
            if not request.video_paths:
                logger.bind(tag=TAG).info("--->4. 根据关键词搜索视频素材")
                material_directory = os.path.join(common_utils.task_dir(story_id), 'materials')
                video_paths = get_video_materials(request.video_terms, request.video_aspect, request.video_concat_mode,
                                                  material_directory, request.voice_duration)
                request.video_paths = video_paths
                self._save_script_story_video_paths(story_id, ','.join(video_paths))
                state_manager.update_task(progress=50)
            script_story_clip_service = ScriptStoryClipService()
            # 5. 视频剪辑合成
            if not request.combined_video_path:
                state_manager.update_task(progress=52)
                logger.bind(tag=TAG).info("--->5. 视频剪辑合成")
                combined_video_path = os.path.join(common_utils.task_dir(story_id), "combined-video.mp4")
                script_story_clip_service.combine_videos(combined_video_path=combined_video_path,
                                                         video_paths=request.video_paths,
                                                         audio_file=request.voice_file,
                                                         video_aspect=request.video_aspect,
                                                         video_concat_mode=request.video_concat_mode,
                                                         video_transition_mode=request.video_transition_mode,
                                                         threads=request.n_threads)
                self._save_script_story_combined_video_path(story_id, combined_video_path)
                state_manager.update_task(progress=75)
                request.combined_video_path = combined_video_path
            # 6. 视频剪辑合成
            if not request.final_video_url:
                state_manager.update_task(progress=77)
                logger.bind(tag=TAG).info("--->6. 视频剪辑合成")
                final_video_path = os.path.join(common_utils.task_dir(story_id), "final-video.mp4")
                script_story_clip_service.generate_video(
                    video_path=request.combined_video_path,
                    voice_file=request.voice_file,
                    subtitle_file=request.subtitle_file,
                    output_file=final_video_path,
                    params=request
                )
                logger.bind(tag=TAG).info(f"生成视频完成: {final_video_path}")
                state_manager.update_task(progress=90)
                # 存入到minio服务器
                bucket_utils = MinioBucketUtils()
                final_video_url = bucket_utils.local_upload_file(file_path=final_video_path,
                                                                 object_name=story_id + '.mp4')
                self._save_script_story_final_video_url(story_id, final_video_url, request.bgm_file)
                state_manager.update_task(progress=100, state=const.TASK_STATE_COMPLETE)
                logger.bind(tag=TAG).info(f"生成视频完成: {final_video_url}")
        except Exception as e:
            logger.bind(tag=TAG).opt(exception=True).error(f"生成脚本视频失败: {str(e)}")
            state_manager.update_task(state=const.TASK_STATE_FAILED)
            raise

    def script_story_generator(self, request: ScriptStoryParams, user_id: str):
        if not request.story_id:
            request.story_id = snow.next_id()
        state_manager = RedisStateManager(request.story_id)
        # 启动并提交异步任务
        executor.submit(self._process_story_drama_async, request, user_id, state_manager)

    def script_story_status(self, story_id: str):
        state_manager = RedisStateManager(story_id)
        task = state_manager.get_task()
        if task:
            return {
                'state': str(task['state']),
                'progress': int(task['progress'])
            }
        else:
            return {
                'state': '1',
                'progress': 0
            }

    def script_story_detail(self, story_id):
        session = self._get_session()
        try:
            statement = select(ScriptStorySchema).where(
                ScriptStorySchema.id == story_id
            )
            story = session.exec(statement).one()
            if not story:
                raise Exception("未找到脚本故事数据")
            return {
                "id": str(story.id),
                "task_id": str(story.task_id),
                "story_subject": story.story_subject,
                "video_script": story.video_script,
                "video_aspect": VideoAspect(story.video_aspect) if story.video_aspect else None,
                "video_concat_mode": VideoConcatMode(story.video_concat_mode) if story.video_concat_mode else None,
                "video_transition_mode": VideoTransitionMode(story.video_transition_mode)
                if story.video_transition_mode else None,
                "video_clip_duration": story.video_clip_duration,
                "video_source": story.video_source,
                "video_paths": (
                    [p.strip() for p in story.video_paths.split(',')]
                    if story.video_paths and story.video_paths.strip()
                    else []
                ),
                "final_video_url": story.final_video_url,
                "combined_video_path": story.combined_video_path,
                "voice_file": story.voice_file,
                "voice_name": story.voice_name,
                "voice_volume": story.voice_volume,
                "voice_rate": story.voice_rate,
                "voice_duration": story.voice_duration,
                "bgm_type": story.bgm_type,
                "bgm_volume": story.bgm_volume,
                "subtitle_position": story.subtitle_position,
                "custom_position": story.custom_position,
                "font_name": story.font_name,
                "text_fore_color": story.text_fore_color,
                "text_background_color": story.text_background_color,
                "font_size": story.font_size,
                "stroke_color": story.stroke_color,
                "stroke_width": story.stroke_width
            }
        finally:
            session.close()
