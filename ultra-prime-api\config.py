import os
import sys

from loguru import logger


class Config:
    SERVER_VERSION = "1.0.0"
    APP_REQUEST_PROXY = {}
    # docker部署时候要挂载 /opt/ultra
    APP_TMP_STORAGE = os.getenv('APP_TMP_STORAGE', '/opt/ultra/tmp-data')
    APP_VOICE_STORAGE = os.getenv('APP_VOICE_STORAGE', '/opt/ultra/voice-data')
    APP_ENABLE_AUTH = False
    APP_GATEWAY_URL = 'http://127.0.0.1:8800'
    APP_MONGO_DB = 'ultra_prime'
    # 通用大模型
    OPENAI_CHAT_MODEL = 'deepseek-ai/DeepSeek-V3'
    OPENAI_IMAGE_MODEL = 'Kwai-Kolors/Kolors'
    OPENAI_API_URL = 'https://api.siliconflow.cn/v1'
    OPENAI_API_KEY = 'sk-wavqobhdupomrctrvxpweciqffighhizqkqjlhjzkpvtcpct'
    # 阿里巴巴 DASHSCOPE 平台
    DASHSCOPE_API_KEY = "sk-b59cdbf85e564c0789e73f57a6e1e125"
    DASHSCOPE_API_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    DASHSCOPE_CHAT_MODEL = "qwen-plus"
    DASHSCOPE_IMAGE_MODEL = "wanx2.1-t2i-plus"  # 2毛1个
    DASHSCOPE_VIDEO_MODEL = "wan2.2-i2v-plus"  # 3.5元/个
    DASHSCOPE_VIDEO_TASK_URL = "https://dashscope.aliyuncs.com/api/v1/services/aigc/video-generation/video-synthesis"
    DASHSCOPE_VIDEO_TASK_QUERY_URL = "https://dashscope.aliyuncs.com/api/v1/tasks/"
    # Cosyvoice2接口
    COSYVOICE_API_URL = os.getenv("COSYVOICE_API_URL", "http://192.168.3.203:8799")
    # MCP 服务器
    MCP_SERVER = {
        "UltraPrimeMcp": {
            "url": os.getenv('MCP_SERVER', 'http://127.0.0.1:15101/mcp-server/mcp'),
            "transport": "streamable_http"
        },
    }
    # REDIS服务端
    REDIS_URI = os.getenv('REDIS_URI', '192.168.3.203')
    REDIS_PORT = 6379
    REDIS_PASSWORD = os.getenv('REDIS_PASSWORD', None)
    # 数据库
    MYSQL_DATABASE_URI = os.getenv('MYSQL_DATABASE_URI',
                                   'mysql+pymysql://ultra:Ultra#233@192.168.3.203:3306/ultra_prime')
    MONGO_URI = os.getenv('MONGO_URI', '************************************************************')
    # token密钥
    JWT_SECRET = '1234'
    # 微信小程序
    WX_APP_ID = 'wx7a604a01daf0b5aa'
    WX_APP_SECRET = '59aae19d564aaf9c85cdcce6f7930267'
    # aliyun-oss
    OSS_ACCESS_KEY = 'LTAI5tA83ZjDFRxxvU7tzes6'
    OSS_SECRET_KEY = '******************************'
    OSS_END_POINT = 'cn-hangzhou.oss.aliyuncs.com'
    OSS_BUCKET_NAME = 'ultra-prime-img'
    OSS_DOMAIN = 'https://ultra-prime-img.oss-cn-hangzhou.aliyuncs.com'
    # minio
    MINIO_ENDPOINT = os.getenv('MINIO_ENDPOINT', '192.168.3.203:7510')
    MINIO_ACCESS_KEY = os.getenv('MINIO_ACCESS_KEY', 'uMrsAboTJJgCihNsZZYa')
    MINIO_SECRET_KEY = os.getenv('MINIO_SECRET_KEY', 'K0Tsk13a7WCmMlCAkZ7npsLQFXg5Xa3ugTFTPcG9')
    MINIO_BUCKET_NAME = 'prime'
    # pexels
    PEXELS_API_VIDEO_URL = 'https://api.pexels.com/videos/search'
    PEXELS_API_KEY = 'pWjhSRjKswUx2aPRZvMoGxnlnoZzbBz3ntEDuZ5oj02qBmidU2Am1pw9'


_LOGGER_INITIALIZED = False


def setup_logging(
        log_dir="tmp",
        log_file="server.log",
        log_level="INFO",
        file_rotation="10 MB",
        file_retention="30 days"
):
    def formatter(record):
        """为没有 tag 的日志添加默认值"""
        record["extra"].setdefault("tag", record["name"])
        return record["message"]

    global _LOGGER_INITIALIZED

    if not _LOGGER_INITIALIZED:
        # 日志目录
        os.makedirs(log_dir, exist_ok=True)
        os.makedirs("data", exist_ok=True)

        # 日志格式
        log_format = (
            "<green>{time:YYMMDD HH:mm:ss}</green>"
            "[{version}_{extra[selected_module]}]"
            "[<light-blue>{extra[tag]}</light-blue>]"
            "-<level>{level}</level>-<light-green>{message}</light-green>"
        )
        log_format_file = (
            "{time:YYYY-MM-DD HH:mm:ss} - {version}_{extra[selected_module]} - "
            "{name} - {level} - {extra[tag]} - {message}"
        )

        # 版本号和模块
        selected_module_str = "00000000000000"
        log_format = log_format.replace("{version}", Config.SERVER_VERSION)
        log_format = log_format.replace("{selected_module}", selected_module_str)
        log_format_file = log_format_file.replace("{version}", Config.SERVER_VERSION)
        log_format_file = log_format_file.replace("{selected_module}", selected_module_str)

        # 配置 loguru
        logger.remove()
        logger.configure(extra={"selected_module": selected_module_str})
        logger.add(sys.stdout, format=log_format, level=log_level, filter=formatter)
        logger.add(
            os.path.join(log_dir, log_file),
            format=log_format_file,
            level=log_level,
            filter=formatter,
            rotation=file_rotation,
            retention=file_retention,
            compression=None,
            encoding="utf-8",
            enqueue=True,
            backtrace=True,
            diagnose=True,
        )
        _LOGGER_INITIALIZED = True

    return logger
