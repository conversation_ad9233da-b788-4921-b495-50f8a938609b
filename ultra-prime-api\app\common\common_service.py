from app.common.common_model import OnlyOfficeCallbackRequest
from config import setup_logging

TAG = __name__
logger = setup_logging()

class CommonService:
    def only_office_callback(self, request: OnlyOfficeCallbackRequest):
        if request.status == 1:
            logger.bind(tag=TAG).info("OnlyOffice文档打开成功")
            return {
                'error': "0"
            }
        if request.status == 2 or request.status == 3:
            logger.bind(tag=TAG).info("OnlyOffice保存文件")
            return {'error': "1"}

        logger.bind(tag=TAG).info("OnlyOffice<UNK>")
        return {
            'error': "-1"
        }
