from typing import List, Optional

from pydantic import BaseModel


class NovelBasicInfo(BaseModel):
    novel_id: Optional[str] = None
    user_id: Optional[str] = None
    novel_cover: Optional[str] = None
    novel_name: Optional[str] = None
    chapter_count: Optional[int] = None
    words_per_chapter: Optional[int] = None
    novel_author: Optional[str] = None
    novel_desc: Optional[str] = None


class NovelEditParams(BaseModel):
    novel_id: str
    novel_param: str
    content: str


class NovelChapter(BaseModel):
    chapter_no: str
    chapter_name: str
    position: str
    rhythm: str
    attribute_changes: str
    character_development: str
    foreshadow: str


class NovelChapterVol(BaseModel):
    vol: str
    chapters: List[NovelChapter]


class NovelChapterBlueprint(BaseModel):
    blueprint: List[NovelChapterVol]
