from fastapi import APIRouter, Depends

from app.image.image_model import ImageGenerationRequest
from app.image.image_service import ImageOpenAIService, ImageDashscopeService
from app.util.base_model import ApiResponse
from auth import get_current_user
from config import setup_logging

router = APIRouter(prefix="/image", tags=["image路由"])
TAG = __name__
logger = setup_logging()


@router.post("/generate_image_task/{model}", summary="提交图片生成任务")
async def generate_image_task(request: ImageGenerationRequest, model: str = 'local', user=Depends(get_current_user)):
    if model == 'local':
        image_service = ImageOpenAIService()
    elif model == 'aliyun':
        image_service = ImageDashscopeService()
    else:
        raise Exception("不支持的模型")
    task_id = image_service.generate_image_task(request, user.user_id)
    return ApiResponse(data=str(task_id))


@router.get("/query_image_task/{task_id}", summary="查询用户所有的图片生成任务")
async def query_image_task(task_id: str, user=Depends(get_current_user)):
    image_service = ImageOpenAIService()
    return ApiResponse(data=image_service.query_image_task(user.user_id, task_id))


@router.get("/query_image_work", summary="查询用户所有的图片")
async def query_image_work(page: int = 1, size: int = 20, user=Depends(get_current_user)):
    image_service = ImageOpenAIService()
    return ApiResponse(data=image_service.query_image_work(page, size, user.user_id))


@router.get("/get_image_style", summary="获取图片风格")
async def get_image_style(user=Depends(get_current_user)):
    image_service = ImageOpenAIService()
    return ApiResponse(data=image_service.get_image_style())
