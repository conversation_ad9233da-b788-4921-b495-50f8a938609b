import ast
from abc import ABC, abstractmethod

import redis

from app.util import const
from config import Config, setup_logging

TAG = __name__
logger = setup_logging()


class BaseStateManager(ABC):
    """
    执行状态管理器
    """

    def __init__(self, task_id: str, ):
        self.task_id = task_id
        self.task_key = f"ultra-task:{self.task_id}"

    @abstractmethod
    def update_task(self, state: int = const.TASK_STATE_PROCESSING, progress: int = 0, **kwargs):
        pass

    @abstractmethod
    def get_task(self):
        pass

    @abstractmethod
    def get_all_tasks(self):
        pass

    @abstractmethod
    def delete_task(self):
        pass


class RedisStateManager(BaseStateManager):
    def __init__(self, task_id):
        super().__init__(task_id)
        self._redis = redis.StrictRedis(host=Config.REDIS_URI, port=Config.REDIS_PORT, db=1,
                                        password=Config.REDIS_PASSWORD)

    def update_task(self, state: int = const.TASK_STATE_PROCESSING, progress: int = 0, **kwargs):
        progress = int(progress)
        logger.info(f"update task: {self.task_id}, state: {state}, progress: {progress}")
        if progress > 100:
            progress = 100
        fields = {
            "task_id": str(self.task_id),
            "state": str(state),
            "progress": progress,
            **kwargs,
        }
        for field, value in fields.items():
            self._redis.hset(self.task_key, field, str(value))
        if progress == 100 and state == const.TASK_STATE_COMPLETE:
            self._redis.expire(self.task_key, 10)
        if state == const.TASK_STATE_FAILED:
            self._redis.expire(self.task_key, 30)

    def get_all_tasks(self):
        pass

    def get_task(self):
        task_data = self._redis.hgetall(self.task_key)
        if not task_data:
            return None
        task = {
            key.decode("utf-8"): self._convert_to_original_type(value)
            for key, value in task_data.items()
        }
        return task

    def delete_task(self):
        self._redis.expire(self.task_key, 60)

    @staticmethod
    def _convert_to_original_type(value):
        value_str = value.decode("utf-8")
        try:
            return ast.literal_eval(value_str)
        except (ValueError, SyntaxError):
            pass
        if value_str.isdigit():
            return int(value_str)
        return value_str
