from fastapi import APIRouter, Depends, Form, UploadFile, File
from starlette.responses import StreamingResponse

from app.novel.novel_model import NovelBasicInfo, NovelEditParams
from app.novel.the_legend_novel_service import TheLegendNovelService
from app.util.base_model import ApiResponse
from auth import get_current_user
from config import setup_logging

router = APIRouter(prefix="/the-legend-novel", tags=["传奇小说创作路由"])
TAG = __name__
logger = setup_logging()

the_legend_novel_service = TheLegendNovelService()


@router.get("/basic-info/{novel_id}", summary="获取小说基本信息")
async def get_novel_basic_info(novel_id: str, user=Depends(get_current_user)):
    logger.bind(tag=TAG).info(f"novel_id={novel_id}")
    return ApiResponse(
        data=the_legend_novel_service.get_novel_basic_info(novel_id, user.user_id)
    )


@router.get("/user-novel-work", summary="获取我的小说")
async def get_user_novel_work(user=Depends(get_current_user)):
    return ApiResponse(
        data=the_legend_novel_service.get_user_novel_work(user.user_id)
    )


@router.delete("/user-novel-work/{novel_id}", summary="删除我的小说")
async def remove_user_novel_work(novel_id: str, user=Depends(get_current_user)):
    logger.bind(tag=TAG).info(f"novel_id={novel_id}")
    return ApiResponse(
        data=the_legend_novel_service.remove_user_novel_work(novel_id, user.user_id)
    )


@router.post("/basic-info-cover/upload", summary="获取小说基本信息")
async def upload_novel_cover(novel_id: str = Form(...),
                             file: UploadFile = File(...), user=Depends(get_current_user)):
    logger.bind(tag=TAG).info(f"novel_id={novel_id}")
    the_legend_novel_service.upload_novel_cover(novel_id, file)
    return ApiResponse()


@router.post("/basic-info", summary="更新小说基本信息")
async def save_novel_basic_info(request: NovelBasicInfo, user=Depends(get_current_user)):
    request.user_id = user.user_id
    the_legend_novel_service.save_novel_basic_info(request)
    return ApiResponse()


@router.get("/creation-params/{novel_id}/{novel_param}", summary="小说创作参数查询")
async def novel_creation_params_query(novel_id: str, novel_param: str, user=Depends(get_current_user)):
    return ApiResponse(data=the_legend_novel_service.novel_creation_params_query(novel_id, novel_param))


@router.post("/creation-params/{novel_id}/{novel_param}", summary="生成小说创作参数")
async def novel_creation_params_generator(novel_id: str, novel_param: str, user=Depends(get_current_user)):
    return StreamingResponse(the_legend_novel_service.novel_creation_params_generator(novel_id, novel_param),
                             media_type="text/event-stream")


@router.put("/creation-params/{novel_id}/{novel_param}", summary="用户自行修改小说创作参数")
async def novel_creation_params_edit(request: NovelEditParams, user=Depends(get_current_user)):
    the_legend_novel_service.novel_creation_params_edit(request)
    return ApiResponse()


@router.get("/chapter-blueprint/{novel_id}", summary="获取小说目录")
async def get_chapter_blueprint(novel_id: str, user=Depends(get_current_user)):
    return ApiResponse(data=the_legend_novel_service.get_chapter_blueprint(novel_id))


@router.post("/finalize-chapter/{novel_id}/{chapter_no}", summary="章节定稿")
async def novel_finalize_chapter(novel_id: str, chapter_no: str, user=Depends(get_current_user)):
    the_legend_novel_service.novel_finalize_chapter(novel_id, chapter_no)
    return ApiResponse()
