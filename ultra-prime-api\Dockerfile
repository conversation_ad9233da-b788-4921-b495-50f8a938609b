# 第一阶段：构建阶段
FROM python:3.12-slim AS builder

# 设置工作目录
WORKDIR /app

# 设置 pip 镜像源并升级 pip
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple && pip install --no-cache-dir --upgrade pip

# 安装依赖并优化缓存
RUN pip install --no-cache-dir langgraph===0.5.1 fastmcp===2.9.2 langchain_mcp_adapters===0.1.8 langchain_openai===0.3.18 \
nest_asyncio===1.6.0 asyncio===3.4.3 pymysql===1.1.1 fastapi===0.115.12  starlette===0.46.2 sqlmodel===0.0.24  pyjwt===2.10.1 \
oss2===2.19.1 redis===6.2.0 uvicorn[standard]===0.27.1 fake_useragent===2.2.0 pydantic===2.11.4 langchain===0.3.25 feedparser===6.0.11 \
beautifulsoup4===4.13.4

# 第二阶段：运行阶段
FROM python:3.12-slim

# 设置工作目录
WORKDIR /app

# 从构建阶段复制已安装的依赖
COPY --from=builder /usr/local/lib/python3.12/site-packages /usr/local/lib/python3.12/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# 复制应用代码
COPY . .

# 创建启动脚本
RUN echo '#!/bin/sh\n\
python -m uvicorn main:app --host 0.0.0.0 --port 8000 --env-file .env.production --workers 1 --loop uvloop --http httptools\n\
' > /app/start.sh

RUN chmod +x /app/start.sh

EXPOSE 8000

# 使用启动脚本启动应用
CMD ["/app/start.sh"]

