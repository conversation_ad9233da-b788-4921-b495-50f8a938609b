from typing import List

from fastapi import APIRouter, Depends
from langchain_core.messages import AIMessage, BaseMessage, SystemMessage, HumanMessage
from starlette.responses import StreamingResponse

from app.chat.chat_model import ChatRequest
from app.chat.chat_servce import ChatService
from app.chat.llm_stream import llm_stream_chat
from app.util.base_model import ApiResponse
from auth import get_current_user
from config import setup_logging

router = APIRouter(prefix="/chat", tags=["llm chat 基础路由"])

TAG = __name__
logger = setup_logging()

chat_service = ChatService()


@router.get("/get_agent", summary="获取历史对话")
async def get_agent(user=Depends(get_current_user)):
    return ApiResponse(data=chat_service.get_agent())


@router.get("/history/{agent_id}", summary="获取历史对话")
async def chat_completions(agent_id: str, user=Depends(get_current_user)):
    return ApiResponse(data=chat_service.get_history(user.user_id, agent_id))


@router.delete("/history/{agent_id}", summary="清除历史对话")
async def chat_completions(agent_id: str, user=Depends(get_current_user)):
    chat_service.clear_history(user.user_id, agent_id)
    return ApiResponse()


@router.post("/completions", summary="AI对话流接口(OpenAI流式规范，支持多轮)")
async def chat_completions(request: ChatRequest, user=Depends(get_current_user)):
    """
    多轮AI对话流接口，兼容OpenAI流式chat/completions规范。
    输入：messages: [{role, content}]
    """
    messages: List[BaseMessage] = []
    for msg in request.messages:
        if msg.role == "assistant":
            messages.append(AIMessage(content=msg.content))
        elif msg.role == "user":
            messages.append(HumanMessage(content=msg.content))
        else:
            messages.append(SystemMessage(content=msg.content))
    if len(request.messages) == 3:
        # 初始化聊天
        chat_service.init_history(user.user_id, request.agent_id, request.messages)
    else:
        # 获取最后一个role为user的消息的content
        user_content = ""
        for msg in reversed(request.messages):
            if msg.role == "user":
                user_content = msg.content
                break
        chat_service.save_history(user.user_id, request.agent_id, 'user', user_content)
    return StreamingResponse(llm_stream_chat(messages, request.agent_id, user.user_id),
                             media_type="text/event-stream")
