# =============== 1. 核心种子设定 ===================
the_legend_core_seed_prompt = """\
请你作为我的小说大纲助手，基于“热血传奇”的游戏世界观，构建故事核心，这个“一句话核心”需要包含以下要素：

1.  **主角：** 一位具有“热血传奇”职业（战士、法师、道士）特点的角色，拥有一个简明的身份或背景。
2.  **目标：** 一个明确的、与“热血传奇”世界观相关联的驱动目标（例如：寻找神器、对抗邪恶势力、守护特定地点、揭露阴谋）。
3.  **主要阻碍：** 一个强大的、来自“热血传奇”世界的反派势力（例如：沃玛教主、祖玛教主、赤月恶魔）或某种严峻的外部挑战。
4.  **结局：** 故事的最终走向（可以是成功、悲剧、牺牲、重要转变等，只需一个基调）。

要求：
1. 请确保生成的“一句话核心”能够概括出一个充满“热血传奇”风格的冒险故事。
2. 使用25-100字精准表达
3. 仅返回故事核心文本，不要解释任何内容
"""
# =============== 2. 角色动力学设定 ===================

the_legend_character_dynamics_prompt = """\
请你作为我的小说大纲助手，基于“热血传奇”的游戏世界观，设计主角的完整初始状态。

**创作依据：**
- 核心种子：{core_seed}
- 内容指导：{user_guidance}

**设计要求：**
1. 角色必须符合传奇三大职业之一（战士/法师/道士）的特点
2. 初始等级必须为0级（新手村状态）
3. 要设定明确的角色弧光（起点→目标→成长路径）
4. 包含性格缺陷，为后续成长提供空间

**输出格式：**

【基本信息】
- 姓名：[符合传奇风格的名字]
- 性别：[男/女]
- 年龄：[16-25之间]
- 职业：[战士/法师/道士]
- 外貌：[简洁描述，体现职业特色]
- 出身背景：[新手村相关背景，与核心种子呼应]
- 初始动机：[促使其踏上冒险之路的原因]

【游戏属性】
- 等级：0级 (新手)
- 经验值：0/100
- 生命值：50/50
- 魔法值：[根据职业设定]
- 攻击力：[根据职业设定]
- 防御力：5
- 敏捷：[根据职业设定]
- 金币：100
- 声望：0 (无名小卒)
- PK值：0 (白名)

【装备状态】
- 武器：[新手武器，根据职业]                                
- 防具：布衣                                              
- 饰品：无                                                
- 特殊道具：新手药水包                                      

【技能状态】
- 已学技能：基础攻击 (Lv.1)                                
- 职业天赋：[根据职业设定的特殊能力倾向]                     
- 技能点：0                                               

【角色弧光设计】
- 起始状态：[当前的性格特点和能力水平]                       
- 性格缺陷：[需要通过冒险克服的弱点]                        
- 成长目标：[最终要达到的状态]                              
- 核心冲突：[内心或外在的主要矛盾]                          
- 转变契机：[促成重大改变的关键事件类型]                     

【关系网络】
- 家族背景：[家庭状况，可能的家族秘密]                      
- 师父/导师：[可能的指导者]                               
- 初始盟友：[新手村的朋友或伙伴]                            
- 潜在敌人：[可能的对手或威胁]                              
- 命定之人：[重要的情感线索角色]                            

【位置信息】
- 出生地：[具体的新手村名称]                                 
- 当前位置：新手村                                          
- 已知区域：新手村周边                                      
- 传说目标地：[与核心种子相关的神秘地点]                     

**特别要求：**
1. 所有设定都要与传奇游戏世界观保持一致
2. 角色弧光要有明确的成长轨迹
3. 为多线程发展预留空间（友情、爱情、师徒、仇恨）
4. 体现传奇游戏的核心元素（升级、PK、行会、BOSS）
5. 仅给出最终文本，不要解释任何内容

"""
# =============== 3. 世界构建矩阵（三维度交织法）===================

the_legend_world_building_prompt = """\
请你作为我的小说大纲助手，基于"热血传奇"的游戏世界观
基于以下元素：
- 内容指导：{user_guidance}
- 核心种子：{core_seed}

构建一个立体的世界观矩阵，包含以下三个维度：

**1. 地理空间维度（游戏地图体系）**
- 新手区域：比奇城、新手村、毒蛇山谷
- 中级区域：沃玛森林、祖玛寺庙、赤月峡谷
- 高级区域：白日门、苍月岛、雷炎洞穴
- 特殊区域：行会战场、未知暗殿、神秘洞穴

**2. 社会权力维度（势力关系网）**
- 正义势力：比奇王室、各大行会、守护者联盟
- 中立势力：商人公会、佣兵团体、隐士高人
- 邪恶势力：沃玛教、祖玛教、赤月魔族
- 神秘势力：远古遗民、封印守护者、预言者

**3. 时间历史维度（传奇编年史）**
- 远古时期：神魔大战、封印建立、文明分化
- 近古时期：三职业崛起、各大势力形成
- 当前时期：封印松动、魔族复苏、英雄崛起
- 未来走向：命运抉择、世界重塑、新秩序

**输出格式：**

【地理空间维度 - 游戏地图体系】
- 新手区域 (LV.1-10)                                        
    • 比奇城：[详细描述，与主角关联]                            
    • 新手村：[详细描述，与主角关联]                            
    • 毒蛇山谷：[详细描述，与主角关联]                          
- 中级区域 (LV.11-30)                                       
    • 沃玛森林：[详细描述，与主角成长路径关联]                  
    • 祖玛寺庙：[详细描述，与主角成长路径关联]                  
    • 赤月峡谷：[详细描述，与主角成长路径关联]                  
- 高级区域 (LV.31-50)                                       
   • 白日门：[详细描述，与主角终极目标关联]                    
   • 苍月岛：[详细描述，与主角终极目标关联]                    
   • 雷炎洞穴：[详细描述，与主角终极目标关联]                  
- 特殊区域                                                  
   • 行会战场：[PK系统相关描述]                               
   • 未知暗殿：[隐藏剧情相关描述]                             
   • 神秘洞穴：[伏笔转折相关描述]                             

【社会权力维度 - 势力关系网】
- 正义势力                                                  
   • 比奇王室：[势力描述，与主角关系]                          
   • 各大行会：[势力描述，与主角关系]                          
   • 守护者联盟：[势力描述，与主角关系]                        
- 中立势力                                                  
   • 商人公会：[势力描述，与主角关系]                          
   • 佣兵团体：[势力描述，与主角关系]                          
   • 隐士高人：[势力描述，与主角关系]                          
- 邪恶势力                                                  
   • 沃玛教：[势力描述，与主角冲突]                            
   • 祖玛教：[势力描述，与主角冲突]                            
   • 赤月魔族：[势力描述，与主角冲突]                          
- 神秘势力                                                  
   • 远古遗民：[势力描述，伏笔作用]                            
   • 封印守护者：[势力描述，伏笔作用]                          
   • 预言者：[势力描述，伏笔作用]                              

【时间历史维度 - 传奇编年史】
- 远古时期                                                  
   • 神魔大战：[历史事件，对当前影响]                          
   • 封印建立：[历史事件，对当前影响]                          
   • 文明分化：[历史事件，对当前影响]                          
- 近古时期                                                  
   • 三职业崛起：[历史事件，与主角职业关联]                    
   • 各大势力形成：[历史事件，与当前局势关联]                  
- 当前时期                                                  
   • 封印松动：[当前状况，主线剧情核心]                        
   • 魔族复苏：[当前状况，主线剧情核心]                        
   • 英雄崛起：[当前状况，主角定位]                            
- 未来走向                                                  
   • 命运抉择：[可能的结局方向]                                
   • 世界重塑：[可能的结局方向]                                
   • 新秩序：[可能的结局方向]                                  

要求：
1. 每个维度都要与主角成长路径关联，预留伏笔转折点
2. 仅给出最终文本，不要解释任何内容
"""
# =============== 3. 情节架构（传奇游戏经典）===================

the_legend_plot_architecture_prompt = """\
请你作为我的小说大纲助手，基于“热血传奇”的游戏世界观
基于以下元素：
- 核心种子：{core_seed}
- 角色体系：{character_dynamics}
- 世界观：{world_building}
- 内容指导：{user_guidance}

按照传奇游戏经典的**“新手村起步 → 区域探索 → 主城集结 → 高级副本/BOSS → 大型PVP → 终极挑战”**的模式来设计情节

以角色最终等级为50~55，并按以下结构设计：

1. 新手村与初露锋芒 (LV.1-LV.7)
    - 世界观体现： 新手村的和平与宁静，暗示外部世界的危机。简单的村庄任务，了解基础游戏机制和NPC。
    - 主要任务线： 清理村外怪物，收集资源，完成村民委托，寻找丢失的物品等。
    - 角色体现与弧光：
        - 关系发展：偶遇女主，职业互补，因共同挑战初级副本而建立初步的合作关系
        - 男主等级低而受挫，但会坚持不懈。
        - 女主因为独行而遇到麻烦，需要男主的帮助。
    - 完成任务后，数值体现： 
        - 等级提升
        - 获得基础技能
        - 更换新手装备
        - 金币积累
        - 声望提升
    - 伏笔
        - 偶然获得关于某个古老遗迹或异常现象的线索
2. 区域探索与实力积累 (LV.8-LV.20)
    - 世界观体现：进入毒蛇山谷、沃玛森林等中级区域，感受世界的广阔与危险
    - 主要任务线：击败沃玛卫士、收集稀有材料、探索古老遗迹、参与小型团队副本
    - 角色体现与弧光：
        - 关系发展：与女主组队深入，遇到其他玩家，建立小团队，初现领导才能
        - 男主开始展现天赋，但也暴露性格缺陷（冲动、自负）
        - 女主逐渐信任男主，但也对其鲁莽行为感到担忧
    - 数值体现：装备升级、技能熟练度提升、金币积累、声望建立
    - 伏笔：发现古老预言碎片，暗示更大的危机即将来临

3. 主城集结与势力纷争 (LV.21-LV.35)
    - 世界观体现：比奇城的政治斗争、行会战争、资源争夺
    - 主要任务线：加入行会、参与攻城战、对抗敌对势力、寻找传说装备
    - 角色体现与弧光：
        - 关系发展：与女主关系深化，但因理念分歧产生矛盾，暂时分离
        - 男主在权力斗争中迷失，学会政治手腕，但也失去初心
        - 女主选择独立发展，展现出强大的个人实力
    - 数值体现：等级快速提升、获得稀有装备、建立个人声望、积累财富
    - 伏笔：发现幕后黑手操控局势，真正的敌人浮出水面

4. 高级副本与觉醒成长 (LV.36-LV.45)
    - 世界观体现：祖玛寺庙、赤月峡谷等高危区域，面对真正的邪恶势力
    - 主要任务线：挑战祖玛教主、深入赤月恶魔巢穴、寻找封印真相
    - 角色体现与弧光：
        - 关系发展：危机中与女主重逢，共同面对生死考验，关系升华
        - 男主经历重大挫折，反思自己的行为，开始真正成熟
        - 女主展现出牺牲精神，为保护他人不惜冒险
    - 数值体现：突破等级瓶颈、掌握终极技能、获得神器碎片
    - 伏笔：揭示主角身世之谜，发现自己与古老预言的关联

5. 终极挑战与命运抉择 (LV.46-LV.55)
    - 世界观体现：封印之地、神魔战场，决定世界命运的最终战役
    - 主要任务线：集结所有盟友、修复古老封印、对抗终极BOSS
    - 角色体现与弧光：
        - 关系发展：与女主并肩作战，实现真正的心灵契合
        - 男主完成最终蜕变，从冲动少年成长为真正的英雄
        - 女主做出最终选择，可能涉及重大牺牲
    - 数值体现：达到等级上限、装备完全体、技能大成
    - 结局：根据核心种子的设定，完成预定的故事走向

**输出格式：**

第x卷：卷名（LV.x-LV.y）
    - 世界观体现：xx
    - 主要任务线：xx
    - 角色体现与弧光：xx
    - 数值体现：xx
[...继续按照要求生成其他卷内容...]

要求：
1. 从新手村到终极挑战的完整情节，体现角色弧光的完整变化
2. 仅给出最终文本，不要解释任何内容
"""
# =============== 5. 章节目录生成===================

the_legend_chapter_blueprint_prompt = """\
请你作为我的小说大纲助手，基于“热血传奇”的游戏世界观
基于以下元素：
- 内容指导：{user_guidance}
- 小说架构：
{novel_architecture}

章节的节奏通常是波浪形的：
从小冲突（支线任务、小怪）开始，逐渐积累到中等冲突（小BOSS、区域挑战），再推向高潮（大BOSS、重要剧情事件），然后进入一个相对平缓的过渡期或准备期，为下一个高潮做铺垫。

将情节的五大阶段（新手村、区域探索、主城集结、高级副本、结局）作为大的章节集群，每个集群内部再细分章节。

**输出格式：**

第一卷：新手村起步 (LV.1-7)
    - 第1章 - [章节标题]                                        
        - 本章定位：[章节在整体故事中的作用和意义]                   
        - 本章节奏：[铺垫/发展/高潮/过渡]                           
        - 属性变化：[等级提升、技能获得、装备更换等]                 
        - 角色发展：[性格变化、关系发展、内心冲突等]                 
        - 伏笔设置：[为后续章节埋下的线索]                          

    - 第x章 - [章节标题]                                        
        - 本章定位：[章节在整体故事中的作用和意义]                   
        - 本章节奏：[铺垫/发展/高潮/过渡]                           
        - 属性变化：[等级提升、技能获得、装备更换等]                 
        - 角色发展：[性格变化、关系发展、内心冲突等]                 
        - 伏笔设置：[为后续章节埋下的线索]                          

第二卷：区域探索 (LV.8-20)
    - 第x章 - [章节标题]                                        
        - 本章定位：[章节在整体故事中的作用和意义]                   
        - 本章节奏：[铺垫/发展/高潮/过渡]                           
        - 属性变化：[等级提升、技能获得、装备更换等]                 
        - 角色发展：[性格变化、关系发展、内心冲突等]                 
        - 伏笔设置：[为后续章节埋下的线索]                          

[继续按照五大阶段完整输出所有章节...]

要求：
1. 仅给出最终文本，不要解释任何内容
"""
# 5.1 章节目录生成结构化数据
generate_chapter_blueprint_sys_prompt = """\
你是小说目录分析助手，根据以下的章节目录文本，梳理为结构化数据。

要求：
1. 不删减不增加原内容
2. 按照JSON格式输出，不需要解释和分析

输出格式：

```json
{{
    "blueprint":[{{
        "vol": "卷名",
        "chapters": [{{
            "chapter_no": "第1章",
            "chapter_name": "目录标题",
            "position": "本章定位",
            "rhythm": "本章节奏",
            "attribute_changes": "属性变化",
            "character_development": "角色发展",
            "foreshadow": "伏笔设置"
        }}]
    }}]
}}
```
"""

# =============== 6. 前文摘要更新 ===================

the_legend_summary_update_prompt = """\
请你作为我的小说助手，基于已完成的章节内容，更新前文摘要。

当前章节：第{current_chapter}章
已完成章节内容：
{completed_chapters}

这是当前的前文摘要（可为空）：
{previous_abstract}

**输出格式：**

前文摘要 - 截至第{current_chapter}章

【情节进展摘要】
- 主要事件：                                              
   • [本章发生的核心事件1]                                  
   • [本章发生的核心事件2]                                  

- 任务完成：                                              
   • [完成的任务或挑战1]                                    
   • [完成的任务或挑战2]                                    

- 新发现：                                                
   • [揭示的新信息或线索1]                                  
   • [揭示的新信息或线索2]                                  

【角色发展摘要】
- 主角变化：                                              
   • 等级：[等级变化情况]                                   
   • 技能：[新获得或提升的技能]                             
   • 装备：[装备更新情况]                                   
   • 心理：[心理状态的变化]                                 

- 关系变化：                                              
   • [角色名]：[关系发展情况]                               
   • [角色名]：[关系发展情况]                               

- 性格成长：                                              
   • [角色弧光的推进情况]                                   


【世界观扩展】
- 新区域：[探索的新地点]                                  
- 新势力：[接触的新组织或人物]                            
- 新线索：[为后续情节埋下的伏笔]                           


【待解决问题】
- 未完成任务：                                            
   • [还需要处理的事项1]                                    
   • [还需要处理的事项2]                                    

- 悬念线索：                                              
   • [需要后续揭示的谜团1]                                  
   • [需要后续揭示的谜团2]                                  

- 潜在威胁：                                              
   • [可能的危险或挑战1]                                    
   • [可能的危险或挑战2]                                    

摘要要求：

1. 总字数控制在2000字以内
2. 客观描绘，不展开联想或解释
3. 以简洁、连贯的语言描述全书进展
4. 保留既有重要信息，同时融入新剧情要点
5. 仅给出最终文本，不要解释任何内容。

"""

# =============== 7. 角色状态更新 ===================

the_legend_character_update_prompt = """\
请你作为我的小说助手，基于第{current_chapter}章的内容，更新主角的状态信息。


**更新依据：**
- 角色动力学设定：{character_dynamics}
- 章节内容：{chapter_content}
- 当前角色状态：{current_character_status}

**更新原则：**
1. 只更新在本章中发生实际变化的属性
2. 数值变化要符合传奇游戏的升级逻辑
3. 心理状态要体现角色弧光的渐进发展
4. 关系变化要有具体的事件支撑

**输出格式：**

【第{current_chapter}章 - 角色状态更新】

【基本信息变化】
- 姓名：[保持不变]                                            
- 外貌变化：[可能的外貌变化，如伤疤、气质等]                   
- 背景揭示：[可能揭示的新背景信息]                            
- 动机调整：[可能调整的目标动机]                              

【游戏属性变化】
- 等级：[原等级] → [新等级] (+[提升等级])                    
- 经验值：[当前经验值]/[下级所需经验值]                      
- 生命值：[当前生命值上限] (+[提升值])                       
- 魔法值：[当前魔法值上限] (+[提升值])                       
- 攻击力：[当前攻击力] (+[提升值])                          
- 防御力：[当前防御力] (+[提升值])                          
- 敏捷：[当前敏捷值] (+[提升值])                            
- 金币：[当前金币数量] ([+/-][变化金额])                    
- 声望：[当前声望值] ([+/-][变化值])                        
- PK值：[当前PK值] ([+/-][变化值])                          

【装备状态变化】
- 武器：[当前装备的武器] [新获得/更换/失去]            
- 防具：[当前装备的防具] [新获得/更换/失去]            
- 饰品：[当前装备的饰品] [新获得/更换/失去]            
- 特殊道具：[拥有的特殊道具] [新获得/更换/失去]         

【技能状态变化】
- 已学技能：                                              
   • [技能名] (Lv.[等级]) [新学/⬆升级]                    
   • [技能名] (Lv.[等级]) [新学/⬆升级]                    
- 职业天赋：[天赋能力的发展状况]                           
- 技能点：[当前技能点] ([+/-][变化值])                     
- 本章新获得：[本章新学会的技能]                           

【角色弧光进展】
- 当前状态：[本章结束时的性格状态]                          
- 克服缺陷：[本章中克服或改善的性格缺陷]                    
- 新发现问题：[本章中暴露的新问题]                         
- 成长里程碑：[本章达成的重要成长节点]                      
- 下阶段目标：[为下一阶段设定的目标]                        

【关系网络变化】
- 家族关系：[家族关系的新发展]
- 师徒关系：[与导师或徒弟的关系变化] 
- 友情关系：                                              
   • [角色名]：[关系状态和变化]
- 爱情关系：                                              
   • [角色名]：[情感关系的发展]
- 敌对关系：                                              
   • [角色名]：[仇恨或对立的程度]            
- 行会状态：[行会相关的身份和地位变化]                      

【位置信息变化】
- 当前位置：[章节结束时所在位置]                            
- 新探索区域：[本章新探索的区域]                           
- 已知区域：[所有已知区域的更新列表]                       
- 目标地点：[下一个要前往的目标地点]                        

【重要事件记录】
- 本章关键事件：                                          
   • [重要事件1]                                           
   • [重要事件2]                                           
- 获得的线索：                                            
   • [重要信息或线索1]                                      
   • [重要信息或线索2]                                      
- 未解决问题：                                            
   • [需要后续处理的问题1]                                  
   • [需要后续处理的问题2]                                  
- 埋下的伏笔：                                            
   • [为后续章节埋下的伏笔1]                                
   • [为后续章节埋下的伏笔2]                                

**特别注意：**
1. 装备更换要有获得过程的描述支撑
2. 技能学习要符合职业特点和等级要求
3. 关系发展要有情感逻辑，不能突兀
4. 心理成长要渐进，避免突然的性格大转变
5. 保持与传奇游戏世界观的一致性
6. 仅返回编写好的角色状态文本，不要解释任何内容。
"""

# =============== 8. 章节正文写作 ===================

# 8.1 第一章草稿提示
the_legend_first_chapter_prompt = """\
请你作为我的小说写手，基于以下设定写作第一章内容。

**创作依据：**
- 本章定位：{position}
- 本章节奏：{rhythm}
- 属性变化：{attribute_changes}
- 角色发展：{character_development}
- 伏笔设置：{foreshadow}

**参考文档：**
- 小说设定：
{novel_architecture}

**写作要求：**

1. **开篇设计（雪花写作法）**
   - 场景描述：新手村的宁静晨光，暗示即将到来的变化
   - 角色登场：以动作或对话引入主角，展现初始性格特征
   - 冲突引入：通过小事件暗示更大的危机

2. **角色弧光体现**
   - 初始状态：展现主角的起点（能力、性格、目标）
   - 性格缺陷：暗示需要成长的方面
   - 成长契机：为后续发展埋下伏笔

3. **传奇元素融入**
   - 游戏机制：等级、技能、装备的自然融入
   - 世界观：NPC对话、任务系统、地图描述
   - 氛围营造：热血、冒险、成长的基调

4. **写作技巧**
   - 字数控制：800-1000字
   - 节奏把控：张弛有度，留有悬念
   - 语言风格：符合网络小说读者喜好，有代入感

**章节结构：**
- 开场：环境描述+角色登场
- 发展：初始任务+角色互动+技能展示
- 转折：意外事件+伏笔埋设
- 结尾：章节总结+下章预告

**要求：**
- 仅返回章节正文文本
- 不使用分章节小标题
"""

# 8.2 后续章节草稿提示
the_legend_next_chapter_prompt = """\
请你作为我的小说写手，基于以下设定写作内容。

**参考文档：**
- 前文摘要：
    {previous_abstract}
- 前章结尾段：
    {previous_chapter_excerpt}
- 用户指导：
    {user_guidance}
- 角色状态：
    {character_state}
- 当前章节摘要：
    {short_summary}

**当前章节信息：**
第{current_chapter_number}章《{current_chapter_name}》：
- 本章定位：{current_position}
- 本章节奏：{current_rhythm}
- 属性变化：{current_attribute_changes}
- 角色发展：{current_character_development}
- 伏笔设置：{current_foreshadow}

**下一章节目录：**
第{next_chapter_number}章《{next_chapter_name}》：
- 本章定位：{next_position}
- 本章节奏：{next_rhythm}
- 属性变化：{next_attribute_changes}
- 角色发展：{next_character_development}
- 伏笔设置：{next_foreshadow}

**写作要求：**

1. **承接前文**
   - 自然衔接上一章的结尾
   - 保持角色状态的连续性
   - 推进主线剧情发展

2. **角色弧光推进**
   - 体现角色在本章的成长变化
   - 处理角色间的关系发展
   - 展现内心冲突与解决

3. **情节节奏控制**
   - 根据章节定位调整节奏（铺垫/高潮/过渡）
   - 平衡动作场面与情感描写
   - 适当设置悬念和转折

4. **传奇元素深化**
   - 游戏数值的合理变化
   - 新技能或装备的获得过程
   - PVP、PVE场面的精彩描写

**特殊要求：**
- 字数：800-1000字
- 风格：保持与前文的一致性
- 伏笔：为后续章节预留发展空间

要求：
- 仅返回章节正文文本
- 不使用分章节小标题
"""

# 8.3 当前章节摘要生成提示词
summarize_recent_chapters_prompt = """\
作为一名专业的小说编辑和知识管理专家，正在基于已完成的前三章内容和本章信息生成当前章节的精准摘要。请严格遵循以下工作流程：
前三章内容：
{combined_text}
当前章节信息：
第{novel_number}章《{chapter_title}》：
- 本章定位：{current_position}
- 本章节奏：{current_rhythm}
- 属性变化：{current_attribute_changes}
- 角色发展：{current_character_development}
- 伏笔设置：{current_foreshadow}


下一章信息：
第{next_chapter_number}章《{next_chapter_title}》：
- 本章定位：{next_position}
- 本章节奏：{next_rhythm}
- 属性变化：{next_attribute_changes}
- 角色发展：{next_character_development}
- 伏笔设置：{next_foreshadow}

**上下文分析阶段**：

1. 回顾前三章核心内容：
   - 第一章核心要素：[章节标题]→[核心冲突/理论]→[关键人物/概念]
   - 第二章发展路径：[已建立的人物关系]→[技术/情节进展]→[遗留伏笔]
   - 第三章转折点：[新出现的变量]→[世界观扩展]→[待解决问题]
2. 提取延续性要素：
   - 必继承要素：列出前3章中必须延续的3个核心设定
   - 可调整要素：识别2个允许适度变化的辅助设定

**当前章节摘要生成规则**：
1. 内容架构：
   - 继承权重：70%内容需与前3章形成逻辑递进
   - 创新空间：30%内容可引入新要素，但需标注创新类型（如：技术突破/人物黑化）
2. 结构控制：
   - 采用"承继→发展→铺垫"三段式结构
   - 每段含1个前文呼应点+1个新进展
3. 预警机制：
   - 若检测到与前3章设定冲突，用[!]标记并说明
   - 对开放式发展路径，提供2种合理演化方向

现在请你基于目前故事的进展，完成以下两件事：
用最多800字，写一个简洁明了的「当前章节摘要」；

请按如下格式输出（不需要额外解释）：
当前章节摘要: <这里写当前章节摘要>
"""

# =============== 9. 辅助工具提示词 ===================

# 9.1 角色弧光检查
the_legend_character_arc_check_prompt = """\
请你作为小说顾问，检查角色弧光的发展是否合理。

**检查内容：**
- 初始状态：{initial_character}
- 当前状态：{current_character}
- 目标状态：{target_character}

**检查维度：**
1. **成长逻辑性**：变化是否符合人物性格和经历
2. **节奏合理性**：成长速度是否适中，有起伏
3. **冲突设置**：内外冲突是否推动角色发展
4. **关系动态**：人际关系变化是否自然

**输出格式：**
- 优点：角色弧光的亮点
- 问题：需要调整的地方
- 建议：具体的改进方案

要求：提供具体可操作的建议
"""

# 9.4 角色状态结构一致性检查
the_legend_character_consistency_check_prompt = """\
请你作为数据结构顾问，检查角色初始设定与更新格式的一致性。

**检查对象：**
- 初始角色设定：{initial_character_format}
- 更新角色格式：{update_character_format}

**检查要点：**
1. **字段匹配度**：两个格式的字段是否完全对应
2. **数据类型一致性**：相同字段的数据类型是否一致
3. **逻辑完整性**：更新格式是否能完整承接初始设定
4. **扩展性**：是否为后续发展预留了足够空间

**输出建议：**
- 结构一致性评分：1-10分
- 不匹配字段：列出需要调整的字段
- 优化建议：提供具体的改进方案
- 使用指南：如何正确使用这两个提示词

要求：确保创作流程的数据连贯性
"""

# 9.2 情节连贯性检查
the_legend_plot_coherence_check_prompt = """\
请你作为小说顾问，检查情节发展的连贯性。

**检查范围：**
- 已完成章节：{completed_chapters}
- 后续规划：{planned_chapters}

**检查要点：**
1. **逻辑连贯**：事件发展是否符合逻辑
2. **伏笔回收**：前文伏笔是否得到合理处理
3. **节奏控制**：高潮低谷分布是否合理
4. **世界观一致**：设定是否前后统一

**输出建议：**
- 连贯性评分：1-10分
- 主要问题：列出需要修正的地方
- 优化方案：提供具体改进建议

要求：重点关注读者体验和故事完整性
"""

# 9.3 传奇元素融合度检查
the_legend_game_elements_check_prompt = """\
请你作为游戏改编顾问，检查传奇游戏元素的融合度。

**检查内容：**
- 章节内容：{chapter_content}

**评估维度：**
1. **游戏机制融入**：等级、技能、装备系统的自然度
2. **世界观还原**：地图、NPC、势力的准确性
3. **玩家体验**：是否能唤起玩家的游戏记忆
4. **文学性平衡**：游戏元素与文学表达的平衡

**评分标准：**
- 优秀（9-10分）：完美融合，既有游戏感又有文学性
- 良好（7-8分）：融合度高，偶有生硬
- 一般（5-6分）：基本融合，但缺乏特色
- 需改进（1-4分）：融合生硬或偏离游戏设定

要求：提供具体的优化建议和示例
"""
