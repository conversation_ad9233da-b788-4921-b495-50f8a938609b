import gc
import glob
import os
import random
from typing import List

from PIL import ImageFont
from moviepy import AudioFileClip,afx
from moviepy.video.tools import subtitles

from app.clip import is_nvenc_supported, VideoEffect
from app.util import common_utils
from app.video.video_model import VideoAspect
from config import setup_logging

TAG = __name__
logger = setup_logging()


class FullStoryPoetryClipService:

    def __init__(self):
        self.fps = 30
        self.video_codec = 'libx264'  # 使用 NVIDIA GPU 编码 h264_nvenc
        self.audio_codec = 'aac'
        self.video_effects = VideoEffect()  # 视频特效实例
        if is_nvenc_supported():
            self.video_codec = 'h264_nvenc'
            self.ffmpeg_params = [
                '-y',
                '-preset', 'p7',  # 最快速度
                '-cq', '23'  # 控制质量（类似CRF），值越小质量越高
            ]
        else:
            self.video_codec = 'libx264'
            self.ffmpeg_params = [
                '-y',
                '-preset', 'veryfast',
                # '-crf', '23'
            ]

    def wrap_text(self, text, max_width, font="Arial", fontsize=60):
        # Create ImageFont
        font = ImageFont.truetype(font, fontsize)

        def get_text_size(inner_text):
            inner_text = inner_text.strip()
            left, top, right, bottom = font.getbbox(inner_text)
            return right - left, bottom - top

        width, height = get_text_size(text)
        if width <= max_width:
            return text, height

        processed = True

        _wrapped_lines_ = []
        words = text.split(" ")
        _txt_ = ""
        for word in words:
            _before = _txt_
            _txt_ += f"{word} "
            _width, _height = get_text_size(_txt_)
            if _width <= max_width:
                continue
            else:
                if _txt_.strip() == word.strip():
                    processed = False
                    break
                _wrapped_lines_.append(_before)
                _txt_ = f"{word} "
        _wrapped_lines_.append(_txt_)
        if processed:
            _wrapped_lines_ = [line.strip() for line in _wrapped_lines_]
            result = "\n".join(_wrapped_lines_).strip()
            height = len(_wrapped_lines_) * height
            return result, height

        _wrapped_lines_ = []
        chars = list(text)
        _txt_ = ""
        for word in chars:
            _txt_ += word
            _width, _height = get_text_size(_txt_)
            if _width <= max_width:
                continue
            else:
                _wrapped_lines_.append(_txt_)
                _txt_ = ""
        _wrapped_lines_.append(_txt_)
        result = "\n".join(_wrapped_lines_).strip()
        height = len(_wrapped_lines_) * height
        return result, height

    def close_clip(self, clip):
        if clip is None:
            return

        try:
            # close main resources
            if hasattr(clip, 'reader') and clip.reader is not None:
                clip.reader.close()

            # close audio resources
            if hasattr(clip, 'audio') and clip.audio is not None:
                if hasattr(clip.audio, 'reader') and clip.audio.reader is not None:
                    clip.audio.reader.close()
                del clip.audio

            # close mask resources
            if hasattr(clip, 'mask') and clip.mask is not None:
                if hasattr(clip.mask, 'reader') and clip.mask.reader is not None:
                    clip.mask.reader.close()
                del clip.mask

            # handle child clips in composite clips
            if hasattr(clip, 'clips') and clip.clips:
                for child_clip in clip.clips:
                    if child_clip is not clip:  # avoid possible circular references
                        self.close_clip(child_clip)

            # clear clip list
            if hasattr(clip, 'clips'):
                clip.clips = []

        except Exception as e:
            logger.bind(tag=TAG).info(f"failed to close clip: {str(e)}")

        del clip
        gc.collect()

    def delete_files(self, files):
        """删除文件或文件列表"""
        if isinstance(files, str):
            files = [files]

        for file_path in files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.bind(tag=TAG).info(f"删除文件: {file_path}")
            except Exception as e:
                logger.bind(tag=TAG).warning(f"删除文件失败 {file_path}: {str(e)}")

    def get_bgm_file(self):
        suffix = "*.mp3"
        song_dir = common_utils.song_dir()
        files = glob.glob(os.path.join(song_dir, suffix))
        if files:
            return random.choice(files)
        return ''

    def composite_video(self, video_file_path: str, voice_file_path: str, subtitle_file_path: str,
                        output_file_path: str,
                        video_aspect: VideoAspect = VideoAspect.portrait, ):
        global video_clip, audio_clip
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_file_path)
            os.makedirs(output_dir, exist_ok=True)
            logger.bind(tag=TAG).info(f"输出目录: {output_dir}")
            # 检查输入文件是否存在
            if not os.path.exists(video_file_path):
                raise Exception(f"视频文件不存在: {video_file_path}")
            if not os.path.exists(voice_file_path):
                raise Exception(f"音频文件不存在: {voice_file_path}")
            if not os.path.exists(subtitle_file_path):
                raise Exception(f"字幕文件不存在: {subtitle_file_path}")
            # 已经生成的跳过
            if os.path.exists(output_file_path):
                logger.bind(tag=TAG).info(f"视频文件已存在，跳过合成: {output_file_path}")
                return
            logger.bind(tag=TAG).info(f"开始处理视频合成...")
            # 获取视频尺寸
            aspect = VideoAspect(video_aspect)
            video_width, video_height = aspect.to_resolution()
            logger.bind(tag=TAG).info(f"视频尺寸: {video_width}x{video_height}")
            # 加载音频获取时长
            audio_clip = AudioFileClip(voice_file_path).with_effects(
                [afx.MultiplyVolume(1.0)]
            )
            subs = subtitles.file_to_subtitles(subtitle_file_path, encoding="utf-8")
            logger.bind(tag=TAG).info(f"成功解析 {len(subs)} 行字幕")
            duration = max([tb for ((ta, tb), txt) in subs])
            logger.bind(tag=TAG).info(f"字幕时长: {duration}")

        except Exception as e:
            logger.bind(tag=TAG).error(f"视频合成过程中出错: {str(e)}")
            raise
        finally:
            # 清理资源
            try:
                if 'video_clip' in locals():
                    self.close_clip(video_clip)
                if 'audio_clip' in locals():
                    self.close_clip(audio_clip)
            except Exception as e:
                logger.bind(tag=TAG).info(f"failed to close clip: {str(e)}")

    def combine_videos(self, combined_video_path: str, video_paths: List[str],
                       video_aspect: VideoAspect = VideoAspect.portrait):
        pass
