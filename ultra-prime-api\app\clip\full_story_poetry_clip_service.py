import gc
import glob
import os
import random
from typing import List

from PIL import ImageFont
from moviepy import AudioFileClip, afx, VideoFileClip, CompositeVideoClip, TextClip, concatenate_videoclips, \
    CompositeAudioClip
from moviepy.video.tools import subtitles

from app.clip import is_nvenc_supported, VideoEffect
from app.clip.effects.factory import create_effect
from app.util import common_utils
from app.video.video_model import VideoAspect
from config import setup_logging

TAG = __name__
logger = setup_logging()


class FullStoryPoetryClipService:

    def __init__(self):
        self.fps = 30
        self.video_codec = 'libx264'  # 使用 NVIDIA GPU 编码 h264_nvenc
        self.audio_codec = 'aac'
        self.video_effects = VideoEffect()  # 视频特效实例
        if is_nvenc_supported():
            self.video_codec = 'h264_nvenc'
            self.ffmpeg_params = [
                '-y',
                '-preset', 'p7',  # 最快速度
                '-cq', '23'  # 控制质量（类似CRF），值越小质量越高
            ]
        else:
            self.video_codec = 'libx264'
            self.ffmpeg_params = [
                '-y',
                '-preset', 'veryfast',
                # '-crf', '23'
            ]

    def wrap_text(self, text, max_width, font="Arial", fontsize=60):
        # Create ImageFont
        font = ImageFont.truetype(font, fontsize)

        def get_text_size(inner_text):
            inner_text = inner_text.strip()
            left, top, right, bottom = font.getbbox(inner_text)
            return right - left, bottom - top

        width, height = get_text_size(text)
        if width <= max_width:
            return text, height

        processed = True

        _wrapped_lines_ = []
        words = text.split(" ")
        _txt_ = ""
        for word in words:
            _before = _txt_
            _txt_ += f"{word} "
            _width, _height = get_text_size(_txt_)
            if _width <= max_width:
                continue
            else:
                if _txt_.strip() == word.strip():
                    processed = False
                    break
                _wrapped_lines_.append(_before)
                _txt_ = f"{word} "
        _wrapped_lines_.append(_txt_)
        if processed:
            _wrapped_lines_ = [line.strip() for line in _wrapped_lines_]
            result = "\n".join(_wrapped_lines_).strip()
            height = len(_wrapped_lines_) * height
            return result, height

        _wrapped_lines_ = []
        chars = list(text)
        _txt_ = ""
        for word in chars:
            _txt_ += word
            _width, _height = get_text_size(_txt_)
            if _width <= max_width:
                continue
            else:
                _wrapped_lines_.append(_txt_)
                _txt_ = ""
        _wrapped_lines_.append(_txt_)
        result = "\n".join(_wrapped_lines_).strip()
        height = len(_wrapped_lines_) * height
        return result, height

    def close_clip(self, clip):
        if clip is None:
            return

        try:
            # close main resources
            if hasattr(clip, 'reader') and clip.reader is not None:
                clip.reader.close()

            # close audio resources
            if hasattr(clip, 'audio') and clip.audio is not None:
                if hasattr(clip.audio, 'reader') and clip.audio.reader is not None:
                    clip.audio.reader.close()
                del clip.audio

            # close mask resources
            if hasattr(clip, 'mask') and clip.mask is not None:
                if hasattr(clip.mask, 'reader') and clip.mask.reader is not None:
                    clip.mask.reader.close()
                del clip.mask

            # handle child clips in composite clips
            if hasattr(clip, 'clips') and clip.clips:
                for child_clip in clip.clips:
                    if child_clip is not clip:  # avoid possible circular references
                        self.close_clip(child_clip)

            # clear clip list
            if hasattr(clip, 'clips'):
                clip.clips = []

        except Exception as e:
            logger.bind(tag=TAG).info(f"failed to close clip: {str(e)}")

        del clip
        gc.collect()

    def delete_files(self, files):
        """删除文件或文件列表"""
        if isinstance(files, str):
            files = [files]

        for file_path in files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.bind(tag=TAG).info(f"删除文件: {file_path}")
            except Exception as e:
                logger.bind(tag=TAG).warning(f"删除文件失败 {file_path}: {str(e)}")

    def get_bgm_file(self):
        suffix = "*.mp3"
        song_dir = common_utils.song_dir()
        files = glob.glob(os.path.join(song_dir, suffix))
        if files:
            return random.choice(files)
        return ''

    def composite_video(self, video_file_path: str, voice_file_path: str, subtitle_file_path: str,
                        output_file_path: str,
                        video_aspect: VideoAspect = VideoAspect.portrait):
        global video_clip, audio_clip, final_video
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_file_path)
            os.makedirs(output_dir, exist_ok=True)
            logger.bind(tag=TAG).info(f"输出目录: {output_dir}")
            # 检查输入文件是否存在
            if not os.path.exists(video_file_path):
                raise Exception(f"视频文件不存在: {video_file_path}")
            if not os.path.exists(voice_file_path):
                raise Exception(f"音频文件不存在: {voice_file_path}")
            if not os.path.exists(subtitle_file_path):
                raise Exception(f"字幕文件不存在: {subtitle_file_path}")
            # 已经生成的跳过
            if os.path.exists(output_file_path):
                logger.bind(tag=TAG).info(f"视频文件已存在，跳过合成: {output_file_path}")
                return
            logger.bind(tag=TAG).info(f"开始处理视频合成...")
            # 获取视频尺寸
            aspect = VideoAspect(video_aspect)
            video_width, video_height = aspect.to_resolution()
            logger.bind(tag=TAG).info(f"视频尺寸: {video_width}x{video_height}")

            # 加载音频获取时长
            audio_clip = AudioFileClip(voice_file_path).with_effects(
                [afx.MultiplyVolume(1.0)]
            )
            audio_duration = audio_clip.duration
            logger.bind(tag=TAG).info(f"音频时长: {audio_duration:.2f}秒")

            # 加载视频
            video_clip = VideoFileClip(video_file_path)
            video_duration = video_clip.duration
            logger.bind(tag=TAG).info(f"原始视频时长: {video_duration:.2f}秒")

            # 目标视频时长为5秒
            target_duration = 5.0

            # 如果音频时长大于视频时长，则将视频减速适配音频时长
            if audio_duration > target_duration:
                target_duration = audio_duration
                speed_factor = video_duration / target_duration
                from moviepy import vfx
                video_clip = video_clip.with_effects([vfx.MultiplySpeed(speed_factor)])
                logger.bind(tag=TAG).info(
                    f"音频时长大于5秒，视频减速至 {target_duration:.2f}秒，减速倍数: {speed_factor:.2f}")
            else:
                # 调整视频时长为目标时长
                video_clip = video_clip.with_duration(target_duration)
                logger.bind(tag=TAG).info(f"视频时长调整为: {target_duration:.2f}秒")

            # 调整视频尺寸
            video_clip = video_clip.resized((video_width, video_height))

            # 处理音频时长，如果音频短于视频，前后平均留静音
            if audio_duration < target_duration:
                silence_duration = (target_duration - audio_duration) / 2
                # 在音频前后添加静音
                from moviepy import AudioClip
                silence_before = AudioClip(lambda t: [0, 0], duration=silence_duration)
                silence_after = AudioClip(lambda t: [0, 0], duration=silence_duration)
                from moviepy import concatenate_audioclips
                audio_clip = concatenate_audioclips([silence_before, audio_clip, silence_after])
                logger.bind(tag=TAG).info(f"音频前后各添加 {silence_duration:.2f}秒静音")
            elif audio_duration > target_duration:
                # 音频时长已经调整了视频时长，保持原样
                pass
            else:
                # 音频时长刚好等于目标时长
                audio_clip = audio_clip.with_duration(target_duration)

            # 解析字幕
            subs = subtitles.file_to_subtitles(subtitle_file_path, encoding="utf-8")
            logger.bind(tag=TAG).info(f"成功解析 {len(subs)} 行字幕")

            # 添加字幕
            font_path = os.path.join(common_utils.font_dir(), "STHeitiMedium.ttc")
            logger.bind(tag=TAG).info(f"字体路径: {font_path}")

            # 创建字幕文本剪辑
            text_clips = []
            for ((start_time, end_time), text) in subs:
                if start_time >= target_duration:
                    break  # 超出视频时长的字幕不处理

                # 调整结束时间不超过视频时长
                end_time = min(end_time, target_duration)

                # 文本换行处理
                wrapped_text, text_height = self.wrap_text(text, video_width - 100, fontsize=60)

                text_clip = TextClip(
                    text=wrapped_text,
                    font_size=60,
                    color='white',
                    stroke_color='black',
                    stroke_width=2,
                    method='caption'
                ).with_start(start_time).with_duration(end_time - start_time)

                # 字幕位置设置在底部
                text_clip = text_clip.with_position(('center', video_height - text_height - 100))
                text_clips.append(text_clip)

            logger.bind(tag=TAG).info(f"创建了 {len(text_clips)} 个字幕片段")

            # 合成视频
            if text_clips:
                final_video = CompositeVideoClip([video_clip, *text_clips], size=(video_width, video_height))
            else:
                final_video = CompositeVideoClip([video_clip], size=(video_width, video_height))

            # 添加音频
            final_video = final_video.with_audio(audio_clip)
            logger.bind(tag=TAG).info("音频和字幕添加完成")

            # 输出视频文件
            logger.bind(tag=TAG).info(f"开始写入视频文件: {output_file_path}")
            final_video.write_videofile(
                filename=output_file_path,
                fps=self.fps,
                codec=self.video_codec,
                temp_audiofile_path=output_dir,
                audio_codec=self.audio_codec,
                threads=8,
                write_logfile=False,
                ffmpeg_params=self.ffmpeg_params,
                logger='bar'
            )
            logger.bind(tag=TAG).info("视频文件写入完成")

        except Exception as e:
            logger.bind(tag=TAG).error(f"视频合成过程中出错: {str(e)}")
            raise
        finally:
            # 清理资源
            try:
                if 'video_clip' in locals():
                    self.close_clip(video_clip)
                if 'audio_clip' in locals():
                    self.close_clip(audio_clip)
                if 'final_video' in locals():
                    self.close_clip(final_video)
            except Exception as e:
                logger.bind(tag=TAG).info(f"failed to close clip: {str(e)}")

    def combine_videos(self, combined_video_path: str, video_paths: List[str],
                       video_aspect: VideoAspect = VideoAspect.portrait):
        """
        将视频片段合成一个视频，添加随机转场效果
        :param combined_video_path: 输出视频路径
        :param video_paths: 视频片段路径列表
        :param video_aspect: 视频宽高比
        :return:
        """
        global processed_clips, final_video
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(combined_video_path)
            os.makedirs(output_dir, exist_ok=True)

            # 已经生成的跳过
            if os.path.exists(combined_video_path):
                logger.bind(tag=TAG).info(f"合成视频已存在，跳过: {combined_video_path}")
                return

            if not video_paths:
                raise Exception("视频路径列表为空")

            logger.bind(tag=TAG).info(f"开始合并 {len(video_paths)} 个视频片段...")

            # 获取视频尺寸
            aspect = VideoAspect(video_aspect)
            video_width, video_height = aspect.to_resolution()
            logger.bind(tag=TAG).info(f"目标视频尺寸: {video_width}x{video_height}")

            # 加载和处理视频片段
            processed_clips = []
            total_duration = 0

            # 可用的转场效果列表
            transition_effects = ['fade_in', 'fade_out', 'crossfade_in', 'crossfade_out']

            for i, video_path in enumerate(video_paths):
                logger.bind(tag=TAG).info(f"处理视频 {i + 1}/{len(video_paths)}: {video_path}")
                try:
                    # 检查文件是否存在
                    if not os.path.exists(video_path):
                        logger.bind(tag=TAG).warning(f"视频文件不存在，跳过: {video_path}")
                        continue

                    # 加载视频
                    clip = VideoFileClip(video_path)

                    # 调整视频尺寸
                    clip = clip.resized((video_width, video_height))

                    # 为每个片段添加随机转场效果
                    if i == 0:
                        # 第一个片段添加淡入效果
                        effect = create_effect('fade_in', duration=0.5)
                        clip = effect.apply(clip)
                        logger.bind(tag=TAG).info(f"第一个片段应用淡入效果")
                    elif i == len(video_paths) - 1:
                        # 最后一个片段添加淡出效果
                        effect = create_effect('fade_out', duration=0.5)
                        clip = effect.apply(clip)
                        logger.bind(tag=TAG).info(f"最后一个片段应用淡出效果")
                    else:
                        # 中间片段随机选择转场效果
                        effect_type = random.choice(transition_effects)
                        effect = create_effect(effect_type, duration=0.3)
                        clip = effect.apply(clip)
                        logger.bind(tag=TAG).info(f"片段 {i + 1} 应用转场效果: {effect_type}")

                    processed_clips.append(clip)
                    total_duration += clip.duration
                    logger.bind(tag=TAG).info(f"视频片段处理完成，时长: {clip.duration:.2f}秒")

                except Exception as e:
                    logger.bind(tag=TAG).error(f"处理视频片段失败 {video_path}: {str(e)}")
                    continue

            if not processed_clips:
                raise Exception("没有成功处理的视频片段")

            logger.bind(tag=TAG).info(f"总共处理了 {len(processed_clips)} 个视频片段，总时长: {total_duration:.2f}秒")

            # 合并视频片段
            logger.bind(tag=TAG).info("开始合并视频片段...")
            final_video = concatenate_videoclips(processed_clips, method="compose")
            logger.bind(tag=TAG).info("视频片段合并完成")

            # 添加背景音乐
            bgm_file = self.get_bgm_file()
            if bgm_file and os.path.exists(bgm_file):
                try:
                    bgm_clip = AudioFileClip(bgm_file).with_effects([
                        afx.MultiplyVolume(0.2),  # 背景音乐音量20%
                        afx.AudioFadeOut(3),  # 3秒淡出
                        afx.AudioLoop(duration=final_video.duration),
                    ])

                    # 合并原音频和背景音乐
                    if final_video.audio:
                        combined_audio = CompositeAudioClip([final_video.audio, bgm_clip])
                    else:
                        combined_audio = bgm_clip

                    final_video = final_video.with_audio(combined_audio)
                    logger.bind(tag=TAG).info("背景音乐添加完成")

                    # 清理背景音乐资源
                    bgm_clip.close()
                except Exception as e:
                    logger.bind(tag=TAG).warning(f"添加背景音乐失败: {str(e)}")
            else:
                logger.bind(tag=TAG).info("未找到背景音乐文件，跳过背景音乐")

            # 输出最终视频
            logger.bind(tag=TAG).info(f"开始写入最终视频: {combined_video_path}")
            final_video.write_videofile(
                filename=combined_video_path,
                fps=self.fps,
                codec=self.video_codec,
                audio_codec=self.audio_codec,
                temp_audiofile_path=output_dir,
                threads=8,
                write_logfile=False,
                ffmpeg_params=self.ffmpeg_params,
                logger='bar'
            )
            logger.bind(tag=TAG).info("最终视频文件写入完成")

        except Exception as e:
            logger.bind(tag=TAG).error(f"视频合并过程中出错: {str(e)}")
            raise
        finally:
            # 清理资源
            try:
                if 'processed_clips' in locals():
                    for clip in processed_clips:
                        self.close_clip(clip)
                if 'final_video' in locals():
                    self.close_clip(final_video)
            except Exception as e:
                logger.bind(tag=TAG).info(f"failed to close clip: {str(e)}")
