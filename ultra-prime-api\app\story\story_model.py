from datetime import datetime
from typing import List, Optional, Union

from pydantic import BaseModel, field_serializer
from sqlmodel import SQLModel, Field

from app.util.snowflake_utils import snow
from app.video.video_model import VideoAspect, VideoTransitionMode, VideoConcatMode


class StoryUserWorkItem(BaseModel):
    story_id: str
    type: str
    name: str
    video_url: Optional[str]
    create_time: datetime


class StoryUserWork(BaseModel):
    title: str
    works: List[StoryUserWorkItem]


################FullStory################

class CreateStoryDramaRequest(BaseModel):
    story_content: str
    story_type: str
    story_name: str
    story_scale: str
    story_style: str


class FullStoryCharacterRequest(BaseModel):
    id: str
    story_id: str
    character_name: Optional[str] = None
    character_description: Optional[str] = None
    generate_character_prompt: str
    generate_character_negative_prompt: Optional[str] = None
    character_dub: Optional[str] = None
    character_image_url: Optional[str] = None


class FullStorySceneImageRequest(BaseModel):
    id: str
    story_id: str
    scene_drawing_prompt: str
    scene_drawing_negative_prompt: Optional[str] = None


class FullStorySceneVideoRequest(BaseModel):
    id: str
    story_id: str
    video_generation_prompt: str
    video_generation_negative_prompt: Optional[str] = None


class FullStorySchema(SQLModel, table=True):
    __tablename__ = "aigc_full_story"
    id: int = Field(primary_key=True, title="ID主键")
    user_id: int = Field(title="用户ID")
    story_name: str = Field(title="故事名称")
    story_type: str = Field(title="故事类型")
    story_content: str = Field(title="故事副本")
    story_scale: str = Field(title="故事比例", default="9:16")
    story_style: str = Field(title="故事风格")
    final_video_url: Optional[str] = Field(title="合成视频最终url")
    remark: str = Field(title="备注")
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)

    @field_serializer('id')
    def serialize_id(self, v):
        return str(v)

    @field_serializer('user_id')
    def serialize_user_id(self, v):
        return str(v)


class FullStoryDramaSchema(SQLModel, table=True):
    __tablename__ = "aigc_full_story_drama"
    id: int = Field(default_factory=lambda: str(snow.next_id()), primary_key=True, title="ID主键")
    story_id: int = Field(title="故事ID")
    drama_no: int=Field(title="镜头编号")
    camera_angle: str = Field(title="镜头角度")
    shot_type: str = Field(title="景别")
    camera_movement: str = Field(title="镜头运动")
    characters: str = Field(title="出场人物")
    scene_description: str = Field(title="场景描述")
    scene_drawing_prompt: str = Field(title="生成场景提示词")
    scene_drawing_negative_prompt: str = Field(title="生成场景反向提示词")
    dialogues: str = Field(title="台词对白")
    video_generation_prompt: str = Field(title="生成视频提示词")
    video_generation_negative_prompt: str = Field(title="生成视频反向提示词")
    drama_scene_cover_url: Optional[str] = Field(title="镜头画面生成图")
    drama_video_source_url: Optional[str] = Field(title="镜头视频生成图")
    sound_effect: str = Field(title="音效")
    remark: Optional[str] = Field(title="备注")
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)

    @field_serializer('id')
    def serialize_id(self, v):
        return str(v)

    @field_serializer('story_id')
    def serialize_story_id(self, v):
        return str(v)


class FullStoryCharacterSchema(SQLModel, table=True):
    __tablename__ = "aigc_full_story_character"
    id: int = Field(default_factory=lambda: str(snow.next_id()), primary_key=True, title="ID主键")
    story_id: int = Field(title="故事ID")
    character_name: str = Field(title="人物名称")
    character_description: str = Field(title="人物描述")
    character_dub: Optional[str] = Field(title="人物音色")
    character_image_url: Optional[str] = Field(title="人物图像")
    generate_character_prompt: str = Field(title="生成人物提示词")
    generate_character_negative_prompt: str = Field(title="生成人物反向提示词")
    remark: Optional[str] = Field(title="备注")
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)

    @field_serializer('id')
    def serialize_id(self, v):
        return str(v)

    @field_serializer('story_id')
    def serialize_story_id(self, v):
        return str(v)


class FullStoryDialogueSchema(SQLModel, table=True):
    __tablename__ = "aigc_full_story_dialogue"
    id: int = Field(default_factory=lambda: str(snow.next_id()), primary_key=True, title="ID主键")
    story_id: int = Field(title="故事ID")
    drama_no: int=Field(title="镜头编号")
    role: str = Field(title="角色")
    line: str = Field(title="台词")
    emotion: str = Field(title="台词情感")
    remark: str = Field(title="备注")
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)

    @field_serializer('id')
    def serialize_id(self, v):
        return str(v)

    @field_serializer('story_id')
    def serialize_story_id(self, v):
        return str(v)


################ScriptStory################

class ScriptStoryParams(BaseModel):
    story_id: Optional[str] = None
    story_subject: Optional[str] = None
    video_script: Optional[str] = None
    video_terms: Optional[List[str]] = None
    video_aspect: Optional[VideoAspect] = VideoAspect.portrait  # 分辨率
    video_concat_mode: Optional[VideoConcatMode] = VideoConcatMode.random  # =视频拼接方式
    video_transition_mode: Optional[VideoTransitionMode] = VideoTransitionMode.fade_in  # 转场方式
    video_clip_duration: Optional[int] = 5
    # 视频
    final_video_url: Optional[List[str]] = None
    combined_video_path: Optional[str] = None
    video_source: Optional[str] = "pexels"
    video_paths: Optional[List[str]] = None
    # 配音
    voice_file: Optional[str] = ""
    voice_duration: Optional[float] = 0
    voice_name: Optional[str] = "zh-CN-XiaoxiaoNeural"
    voice_volume: Optional[float] = 1.0
    voice_rate: Optional[float] = 1.0
    # 背景音乐
    bgm_type: Optional[str] = "random"
    bgm_file: Optional[str] = ""
    bgm_volume: Optional[float] = 0.2
    # 字幕
    subtitle_file: Optional[str] = ""
    subtitle_enabled: Optional[bool] = True
    subtitle_position: Optional[str] = "bottom"  # top, bottom, center
    custom_position: Optional[float] = 70.0
    font_name: Optional[str] = "STHeitiMedium.ttc"
    text_fore_color: Optional[str] = "#FFFFFF"
    text_background_color: Optional[Union[bool, str]] = True

    font_size: Optional[int] = 60
    stroke_color: Optional[str] = "#000000"
    stroke_width: Optional[float] = 1.5
    n_threads: Optional[int] = 2
    paragraph_number: Optional[int] = 1


class ScriptStoryWorkResponse(BaseModel):
    id: str
    story_subject: str
    status: str = None
    progress: int
    video_url: Optional[str] = None
    thumbnail: Optional[str] = None
    duration: Optional[float] = None
    video_aspect: str = None
    create_time: str = None


class ScriptStorySchema(SQLModel, table=True):
    __tablename__ = "aigc_script_story"
    id: int = Field(default_factory=lambda: str(snow.next_id()), primary_key=True, title="ID主键")
    user_id: str = Field(title="用户唯一标识，用于权限校验与计费")
    # ------------ 故事元数据 ------------
    story_subject: str = Field(title="故事主题/关键词，用于素材检索与脚本生成")
    video_script: str = Field(title="完整故事脚本（Markdown 格式），可直接用于口播或字幕")
    video_terms: str = Field(
        title="正向提示词/标签，多个用英文逗号分隔或传列表；用于素材匹配")
    # ------------ 视频规格 ------------
    video_aspect: Optional[str] = Field(title="画幅比例枚举，如 '16:9'、'9:16'、'1:1' 等", default="16:9")
    video_concat_mode: Optional[str] = Field(title="片段拼接模式：'sequential'（顺序）、'random'（随机）", default="random")
    video_transition_mode: Optional[str] = Field(title="转场模式：'none'（无）、'fade'（淡入淡出）、'slide'（滑动）",
                                                 default="FadeIn")
    video_clip_duration: int = Field(title="单段素材时长（秒）", default=5)
    # ------------ 素材 & 音频 ------------
    voice_file: Optional[str] = Field(title="配音文件名称")
    voice_duration: Optional[float] = Field(title="配音长度")
    voice_name: Optional[str] = Field(title="音色名称", default="zh-CN-XiaoxiaoNeural")
    voice_volume: float = Field(title="音量增益", default=1.0)
    voice_rate: float = Field(title="语速倍率", default=1.0)

    final_video_url: Optional[str] = Field(title="合成视频最终url")
    combined_video_path: Optional[str] = Field(title="合成本地路径")
    video_source: str = Field(title="素材来源：'pexels'、'getty'、'self_upload' 等", default="pexels")
    video_paths: Optional[str] = Field(title="素材本地路径")

    # ------------ 背景音乐 ------------
    bgm_type: Optional[str] = Field(default="random", title="背景选择类型")
    bgm_file: Optional[str] = Field(title="本地或远程背景音乐文件路径；空字符串表示无背景音乐")
    bgm_volume: float = Field(title="背景音乐音量，范围 0.0-1.0，默认 0.3", default=0.3)
    # ------------ 字幕 ------------
    subtitle_enabled: bool = Field(title="是否生成字幕；true=生成，false=不生成", default=True)
    subtitle_file: str = Field(title="字幕文件")
    subtitle_position: str = Field(title="预设字幕位置：'bottom'、'top'、'center' 等", default="bottom")
    custom_position: float = Field(title="自定义字幕垂直位置百分比（0.0-1.0）；subtitle_position='custom' 时生效",
                                   default=70.0)
    font_name: str = Field(title="字幕字体名称，如 'PingFang SC'；空值使用系统默认", default="STHeitiMedium.ttc")
    text_fore_color: str = Field(
        title="文字前景色，支持十六进制、rgba；如 '#FFFFFF'、'rgba(255,255,255,0.9)'", default="#FFFFFF")
    text_background_color: bool = Field(
        title="文字背景：false=无背景；true=默认半透明黑", default=True)
    font_size: int = Field(title="字号（像素），范围 12-64，默认 36", default=60)
    stroke_color: str = Field(title="描边颜色，格式同 text_fore_color", default="#000000")
    stroke_width: float = Field(title="描边宽度（像素），0=无描边，默认 1.5", default=1.5)
    n_threads: int = Field(title="线程数，范围 1-8，默认 2", default=2)
    paragraph_number: int = Field(title="分段数量，范围 1-10，默认 1", default=1)
    remark: str = Field(title="备注")
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)

    @field_serializer('id')
    def serialize_id(self, v):
        return str(v)

    @field_serializer('user_id')
    def serialize_user_id(self, v):
        return str(v)


################PictureStory################

class PictureStoryAnalysisRequest(BaseModel):
    id: Optional[str] = None
    task_id: str = snow.next_id()

    story_title: str
    story_content: str
    video_aspect: Optional[str] = "16:9"
    voice_name: str


class PictureStorySchema(SQLModel, table=True):
    __tablename__ = "aigc_picture_story"
    id: int = Field(default_factory=lambda: str(snow.next_id()), primary_key=True, title="ID主键")
    user_id: str = Field(title="用户唯一标识")
    # ------------ 故事元数据 ------------
    story_title: str = Field(title="故事标题")
    story_content: str = Field(title="故事内容")
    video_aspect: Optional[str] = Field(title="画幅比例枚举，如 '16:9'、'9:16' 等", default="16:9")
    voice_name: Optional[str] = Field(title="配音名称")
    final_picture_url: Optional[str] = Field(title="合成视频最终url")
    remark: str = Field(title="备注")
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)

    @field_serializer('id')
    def serialize_id(self, v):
        return str(v)

    @field_serializer('user_id')
    def serialize_user_id(self, v):
        return str(v)


class PictureStoryRoleSchema(SQLModel, table=True):
    __tablename__ = "aigc_picture_story_role"
    id: int = Field(default_factory=lambda: str(snow.next_id()), primary_key=True, title="ID主键")
    story_id: int = Field(title="故事ID")
    role_name: str = Field(title="角色名称")
    role_type: str = Field(title="角色类型")
    role_description: str = Field(title="角色描述")
    role_image_url: Optional[str] = Field(title="角色图像")
    role_negative_prompt: str = Field(title="生成角色反向提示词")
    role_prompt: Optional[str] = Field(title="生成角色提示词")
    remark: Optional[str] = Field(title="备注")
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)

    @field_serializer('id')
    def serialize_id(self, v):
        return str(v)

    @field_serializer('story_id')
    def serialize_story_id(self, v):
        return str(v)


class PictureStorySceneSchema(SQLModel, table=True):
    __tablename__ = "aigc_picture_story_scene"
    id: int = Field(default_factory=lambda: str(snow.next_id()), primary_key=True, title="ID主键")
    story_id: int = Field(title="故事ID")
    scene_name: str = Field(title="场景名称")
    narration: str = Field(title="解说旁白")
    role_names: str = Field(title="涉及的角色，目前支持1个角色")
    scene_description: str = Field(title="场景描述")
    scene_image_url: Optional[str] = Field(title="场景图像")
    scene_prompt: str = Field(title="生成场景提示词")
    scene_negative_prompt: str = Field(title="生成场景反向提示词")
    scene_video_status: str = Field(title="场景生成视频状态", default='0')
    remark: Optional[str] = Field(title="备注")
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)

    @field_serializer('id')
    def serialize_id(self, v):
        return str(v)

    @field_serializer('story_id')
    def serialize_story_id(self, v):
        return str(v)
