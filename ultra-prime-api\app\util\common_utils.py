import base64
import hashlib
import os
from typing import Optional

import requests

from config import Config, setup_logging

TAG = __name__
logger = setup_logging()


def md5(text):
    return hashlib.md5(text.encode("utf-8")).hexdigest()


def root_dir():
    return os.path.dirname(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))


def task_dir(sub_dir: str = ""):
    d = os.path.join(Config.APP_TMP_STORAGE, str(sub_dir))
    if not os.path.exists(d):
        os.makedirs(d)
    return d


def resource_dir(sub_dir: str = ""):
    d = os.path.join(root_dir(), "config")
    if sub_dir:
        d = os.path.join(d, str(sub_dir))
    return d


def font_dir(sub_dir: str = ""):
    d = resource_dir("fonts")
    if sub_dir:
        d = os.path.join(d, str(sub_dir))
    if not os.path.exists(d):
        os.makedirs(d)
    return d


def song_dir(sub_dir: str = ""):
    d = resource_dir("songs")
    if sub_dir:
        d = os.path.join(d, str(sub_dir))
    if not os.path.exists(d):
        os.makedirs(d)
    return d


def file_to_base64(file_path: str) -> Optional[str]:
    """
    将音频文件转换为 base64 字符串

    Args:
        file_path: 音频文件路径

    Returns:
        base64 编码的字符串，如果失败返回 None
    """
    try:
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")

        with open(file_path, "rb") as file:
            audio_data = file.read()
            base64_encoded = base64.b64encode(audio_data).decode('utf-8')
            return base64_encoded

    except Exception as e:
        print(f"转换失败: {e}")
        return None


def download_web_image(image_url: str, out_path: str):
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36"
    }
    logger.bind(tag=TAG).info(f"保存到本地路径->out_path: {out_path}")
    response = requests.get(image_url, headers=headers, proxies=Config.APP_REQUEST_PROXY, timeout=(60, 240))
    with open(out_path, "wb") as f:
        f.write(response.content)
