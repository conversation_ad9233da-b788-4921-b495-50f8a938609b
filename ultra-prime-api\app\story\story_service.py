from typing import List

from sqlmodel import Session, select

from app.story.story_model import Story<PERSON><PERSON>Work, PictureStorySchema, FullStorySchema, \
    StoryUserWorkItem, ScriptStorySchema
from config import setup_logging
from dependencies import get_session

TAG = __name__
logger = setup_logging()


class StoryService:
    def _get_session(self) -> Session:
        # 每次操作都获取新的session
        return next(get_session())

    def get_user_story_work(self, user_id: str) -> List[StoryUserWork]:
        session = self._get_session()
        try:
            user_work = []
            full_list = session.exec(select(FullStorySchema).where(FullStorySchema.user_id == user_id)).all()
            picture_list = session.exec(select(PictureStorySchema).where(PictureStorySchema.user_id == user_id)).all()
            script_list = session.exec(select(ScriptStorySchema).where(ScriptStorySchema.user_id == user_id)).all()
            user_work.append(StoryUserWork(
                title='脚本视频',
                works=[
                    StoryUserWorkItem(
                        story_id=str(item.id),
                        type='script',
                        name=item.story_subject,
                        video_url=item.final_video_url,
                        create_time=item.create_time
                    )
                    for item in script_list
                ]
            ))
            user_work.append(StoryUserWork(
                title='绘本视频',
                works=[
                    StoryUserWorkItem(
                        story_id=str(item.id),
                        type='picture',
                        name=item.story_title,
                        video_url=item.final_picture_url,
                        create_time=item.create_time
                    )
                    for item in picture_list
                ]
            ))
            user_work.append(StoryUserWork(
                title='完整创作',
                works=[
                    StoryUserWorkItem(
                        story_id=str(item.id),
                        type='full',
                        name=item.story_name,
                        video_url=item.final_video_url,
                        create_time=item.create_time
                    )
                    for item in full_list
                ]
            ))
            return user_work
        finally:
            session.close()

    def remove_user_story_work(self, mode: str, story_id: str, user_id: str):
        global story
        session = self._get_session()
        try:
            if mode == 'script':
                story = session.exec(select(ScriptStorySchema).where(ScriptStorySchema.id == story_id,
                                                                     ScriptStorySchema.user_id == user_id)).one()
            elif mode == 'picture':
                story = session.exec(select(PictureStorySchema).where(PictureStorySchema.id == story_id,
                                                                      PictureStorySchema.user_id == user_id)).one()
            elif mode == 'full':
                story = session.exec(select(FullStorySchema).where(FullStorySchema.id == story_id,
                                                                   FullStorySchema.user_id == user_id)).one()
            session.delete(story)
            session.commit()
        except Exception as e:
            logger.bind(tag=TAG).error(f"删除用户作品失败: {str(e)}")
        finally:
            session.close()
