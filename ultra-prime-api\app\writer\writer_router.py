from fastapi import APIRouter, Depends
from starlette.responses import StreamingResponse

from app.util.base_model import ApiResponse
from app.writer.writer_model import WriterRequest
from app.writer.writer_service import WriterService
from auth import get_current_user
from config import setup_logging

router = APIRouter(prefix="/writer", tags=["llm chat 基础路由"])

TAG = __name__
logger = setup_logging()


@router.get("/get_agent", summary="获取写手智能体")
async def get_agent(user=Depends(get_current_user)):
    writer_service = WriterService()
    return ApiResponse(data=writer_service.get_agent())


@router.post("/completions", summary="AI对话流接口(OpenAI流式规范)")
async def writer_completions(request: WriterRequest, user=Depends(get_current_user)):
    """
    单轮AI对话流接口，兼容OpenAI流式chat/completions规范。
    """
    writer_service = WriterService()
    return StreamingResponse(writer_service.writer_completions(request, user.user_id), media_type="text/event-stream")
