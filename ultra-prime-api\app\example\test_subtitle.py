import asyncio

from app.subtitle.subtitle_service import SubtitleService
from app.voice.voice_service import VoiceEdgeTtsService

if __name__ == '__main__':
    subtitle_service = SubtitleService()
    text = '我盯着全息投影中扭曲的星光，银心黑洞的引力透镜效应正在将11万光年外的候选行星影像放大成爱因斯坦环——那是人类最后的希望，代号\'蓝岸\'。核聚变引擎的幽蓝光芒舔舐着真空，这台基于EAST装置升级的设备已连续运转142年。根据飞船日志，地球在我们出发后的第80年彻底失去植被覆盖，此刻留守者的后代应该正蜷缩在金星轨道的临时空间站里。'

    # subtitle_service.generate_srt(
    #    audio_file='voice.mp3',
    # )
    voice_service = VoiceEdgeTtsService()
    # split_text = voice_service._split_text_by_punctuation(text)
    # print(split_text)
    asyncio.run(
        voice_service.speech_synthesis_local(text, 'voice.mp3', 'subtitle.srt')
    )
