from datetime import datetime
from typing import Optional

from pydantic import BaseModel, field_serializer
from sqlmodel import SQLModel, Field

from app.util.snowflake_utils import snow


class SpeechSynthesisRequest(BaseModel):
    model: str = 'edge-tts'
    speech_text: str
    voice: str
    rate: int
    pitch: int
    volume: int


class SpeechReferenceRequest(BaseModel):
    reference_id: str
    synthesis_text: str


class SpeechSynthesisMultRole(BaseModel):
    role_name: str
    voice: str
    text: str
    rate: Optional[int] = 0
    pitch: Optional[int] = 0
    volume: Optional[int] = 0


class VoiceReferenceSchema(SQLModel, table=True):
    __tablename__ = "aigc_voice_reference"
    id: int = Field(default_factory=lambda: str(snow.next_id()), primary_key=True, title="ID主键")
    user_id: int = Field(title="用户ID")
    voice_name: Optional[str] = Field(title="音色名称")
    audio_file_url: Optional[str] = Field(title="音频文件Url")
    audio_file_path: Optional[str] = Field(title="音频本地文件路径")
    reference_text: str = Field(title="参考文本")
    remark: Optional[str] = Field(title="备注")
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)

    @field_serializer('id')
    def serialize_id(self, v):
        return str(v)

    @field_serializer('user_id')
    def serialize_user_id(self, v):
        return str(v)


class VoiceDubSchema(SQLModel, table=True):
    __tablename__ = "aigc_voice_dub"
    id: int = Field(default_factory=lambda: str(snow.next_id()), primary_key=True, title="ID主键")
    voice_name: str = Field(title="音色名称")
    voice_code: str = Field(title="音色编码")
    preview_url: str = Field(title="试听文件url")
    voice_description: str = Field(title="音色描述")
    voice_icon: str = Field(title="音色图标")
    voice_status: str = Field(title="音色状态")
    voice_tag: str = Field(title="音色标签")
    remark: Optional[str] = Field(title="备注")
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)

    @field_serializer('id')
    def serialize_id(self, v):
        return str(v)
