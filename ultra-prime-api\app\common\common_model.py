from typing import Optional, List

from pydantic import BaseModel


class OnlyOfficeCallbackAction(BaseModel):
    type: str
    userid: str


class OnlyOfficeCallbackHistory(BaseModel):
    pass


class OnlyOfficeCallbackRequest(BaseModel):
    key: str
    status: int
    url: Optional[str] = None
    history: Optional[OnlyOfficeCallbackHistory] = None
    lastsave: Optional[str] = None
    notmodified: Optional[bool] = None
    users: List[str] | None = None
    action: List[OnlyOfficeCallbackAction] | None = None
    token: str
    filetype: Optional[str] = None
