from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel
from sqlmodel import Field, SQLModel

from app.util.snowflake_utils import snow


class ChatMessage(BaseModel):
    role: str
    content: str


class ChatRequest(BaseModel):
    messages: List[ChatMessage]
    agent_id: str


class ChatAgentClassifySchema(SQLModel, table=True):
    __tablename__ = 'aigc_chat_agent_classify'

    id: int = Field(default_factory=lambda: str(snow.next_id()), primary_key=True, title="ID主键")
    classify_name: str
    remark: Optional[str] = Field(default=None, max_length=1024)
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)


class ChatAgentModelSchema(SQLModel, table=True):
    __tablename__ = 'aigc_chat_agent_model'

    id: int = Field(default_factory=lambda: str(snow.next_id()), primary_key=True, title="ID主键")
    classify_id: int
    agent_icon: str
    agent_name: str
    agent_description: str
    agent_prompt: str
    agent_initial_value: str
    agent_status: str
    remark: Optional[str] = Field(default=None, max_length=1024)
    create_time: datetime = Field(default_factory=datetime.now)
    update_time: datetime = Field(default_factory=datetime.now)
