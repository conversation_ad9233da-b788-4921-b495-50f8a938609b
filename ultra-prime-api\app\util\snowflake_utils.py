import threading
import time


class Snowflake:
    def __init__(self, datacenter_id: int = 1, worker_id: int = 1):
        self.datacenter_id = datacenter_id & 0x1F     # 5 bit
        self.worker_id = worker_id & 0x1F             # 5 bit
        self.sequence = 0
        self.last_timestamp = -1
        self.lock = threading.Lock()

        # 各段位移
        self.DC_SHIFT = 17
        self.WORKER_SHIFT = 12
        self.TIME_SHIFT = 22
        self.EPOCH = 1_609_459_200_000  # 2021-01-01 00:00:00 UTC

    def _now(self) -> int:
        return int(time.time() * 1000)

    def next_id(self) -> int:
        with self.lock:
            ts = self._now()
            if ts < self.last_timestamp:
                raise RuntimeError("Clock moved backwards")
            if ts == self.last_timestamp:
                self.sequence = (self.sequence + 1) & 0xFFF   # 12 bit
                if self.sequence == 0:                        # 这一毫秒用完了
                    while ts <= self.last_timestamp:
                        ts = self._now()
            else:
                self.sequence = 0
            self.last_timestamp = ts

            return ((ts - self.EPOCH) << self.TIME_SHIFT) | \
                   (self.datacenter_id << self.DC_SHIFT) | \
                   (self.worker_id << self.WORKER_SHIFT) | \
                   self.sequence

# 全局单例，进程里任何地方都用这一个实例
snow = Snowflake(datacenter_id=1, worker_id=1)