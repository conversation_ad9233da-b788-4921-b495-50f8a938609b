import os
import random
from typing import List
from urllib.parse import urlencode

import fake_useragent
import requests
from moviepy import VideoFile<PERSON>lip

from app.util import common_utils
from app.video.video_model import VideoAspect, MaterialInfo, VideoConcatMode
from config import Config, setup_logging

TAG = __name__
logger = setup_logging()


def get_video_materials(search_terms: List[str],
                        video_aspect: VideoAspect,
                        video_concat_mode: VideoConcatMode,
                        material_directory: str,
                        audio_duration: float,
                        max_clip_duration: int = 5):
    logger.bind(tag=TAG).info("Getting video materials...")
    valid_video_urls = []
    valid_video_items = []
    found_duration = 0.0
    for search_term in search_terms:
        video_items = search_videos_pexels(
            search_term=search_term,
            minimum_duration=5,
            video_aspect=video_aspect,
        )
        for item in video_items:
            if item.url not in valid_video_urls:
                valid_video_items.append(item)
                valid_video_urls.append(item.url)
                found_duration += item.duration
    video_paths = []
    if video_concat_mode.value == VideoConcatMode.random.value:
        random.shuffle(valid_video_items)
    total_duration = 0.0
    for item in valid_video_items:
        try:
            logger.bind(tag=TAG).info(f"downloading video: {item.url}")
            saved_video_path = save_video(video_url=item.url, save_dir=material_directory)
            if saved_video_path:
                logger.bind(tag=TAG).info(f"video saved: {saved_video_path}")
                video_paths.append(saved_video_path)
                seconds = min(max_clip_duration, item.duration)
                total_duration += seconds
                if total_duration > audio_duration:
                    logger.bind(tag=TAG).info(
                        f"total duration of downloaded videos: {total_duration} seconds, skip downloading more")
                    break

        except Exception as e:
            logger.bind(tag=TAG).error(f"download video failed: {str(e)}")
    return video_paths


def search_videos_pexels(search_term: str, minimum_duration: int = 5,
                         video_aspect: VideoAspect = VideoAspect.portrait) -> List[MaterialInfo]:
    aspect = VideoAspect(video_aspect)
    video_orientation = aspect.name
    video_width, video_height = aspect.to_resolution()
    headers = {
        "Authorization": Config.PEXELS_API_KEY,
        "User-Agent": fake_useragent.UserAgent(os=["Windows"]).random,
    }
    # Build URL
    params = {"query": search_term, "per_page": 20, "orientation": video_orientation}
    query_url = f"{Config.PEXELS_API_VIDEO_URL}?{urlencode(params)}"
    logger.bind(tag=TAG).info(f"searching videos: {query_url}")
    try:
        r = requests.get(query_url, headers=headers, proxies=Config.APP_REQUEST_PROXY, timeout=(30, 60))
        response = r.json()
        video_items = []
        if "videos" not in response:
            logger.error(f"search videos failed: {response}")
            return video_items
        videos = response["videos"]
        # loop through each video in the result
        for v in videos:
            duration = v["duration"]
            # check if video has desired minimum duration
            if duration < minimum_duration:
                continue
            video_files = v["video_files"]
            # loop through each url to determine the best quality
            for video in video_files:
                w = int(video["width"])
                h = int(video["height"])
                if w == video_width and h == video_height:
                    item = MaterialInfo()
                    item.provider = "pexels"
                    item.url = video["link"]
                    item.duration = duration
                    video_items.append(item)
                    break
        return video_items
    except Exception as e:
        logger.error(f"search videos failed: {str(e)}")

    return []


def save_video(video_url: str, save_dir: str = "") -> str:
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    url_without_query = video_url.split("?")[0]
    url_hash = common_utils.md5(url_without_query)
    video_id = f"vid-{url_hash}"
    video_path = f"{save_dir}/{video_id}.mp4"
    if os.path.exists(video_path) and os.path.getsize(video_path) > 0:
        logger.bind(tag=TAG).info(f"video already exists: {video_path}")
        return video_path
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }

    # if video does not exist, download it
    with open(video_path, "wb") as f:
        f.write(requests.get(video_url, headers=headers, proxies=Config.APP_REQUEST_PROXY, timeout=(60, 240)).content)

    if os.path.exists(video_path) and os.path.getsize(video_path) > 0:
        try:
            clip = VideoFileClip(video_path)
            duration = clip.duration
            fps = clip.fps
            clip.close()
            if duration > 0 and fps > 0:
                return video_path
        except Exception as e1:
            try:
                os.remove(video_path)
            except Exception as e2:
                logger.bind(tag=TAG).warning(f"remove video file failed: {video_path} => {str(e2)}")
            logger.bind(tag=TAG).warning(f"invalid video file: {video_path} => {str(e1)}")
    return ""
