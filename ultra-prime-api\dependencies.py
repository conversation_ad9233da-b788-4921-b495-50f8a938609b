import redis
from sqlalchemy import create_engine
from sqlmodel import SQLModel, Session

from config import Config

# 创建数据库引擎
engine = create_engine(Config.MYSQL_DATABASE_URI)

redis = redis.Redis(host=Config.REDIS_URI, port=Config.REDIS_PORT, decode_responses=True)


def create_db_and_tables():
    SQLModel.metadata.create_all(engine)


def get_session():
    with Session(engine) as session:
        yield session


def cache_data(key, value, expire_time=60):
    """
    缓存数据到 Redis
    :param key: 缓存的 key
    :param value: 缓存的值
    :param expire_time: 缓存的过期时间（秒），默认为 60 秒
    """
    redis.set(key, value, ex=expire_time)
    print(f"数据已缓存，key: {key}, value: {value}, 过期时间: {expire_time}秒")


def get_cached_data(key):
    """
    从 Redis 中获取缓存数据
    :param key: 缓存的 key
    :return: 缓存的值，如果 key 不存在或已过期，则返回 None
    """
    value = redis.get(key)
    if value is None:
        print(f"缓存中不存在 key: {key} 或已过期")
    else:
        print(f"获取缓存数据成功，key: {key}, value: {value}")
    return value
